{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport Inputs from './MW/Inputs.vue';\nimport Results from './MW/Results.vue';\nimport { HomeFilled, LogoutOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';\nexport default {\n  name: 'MWTaskView',\n  data() {\n    return {\n      shouldRender: true,\n      // 视频操控\n      scan_signal: false,\n      setTs: false,\n      atTs: 0.0\n    };\n  },\n  components: {\n    Inputs,\n    Results,\n    HomeFilled,\n    LogoutOutlined,\n    ArrowLeftOutlined\n  },\n  beforeRouteEnter(_, __, next) {\n    next(vm => {\n      vm.shouldRender = true;\n    });\n  },\n  methods: {\n    setTsFunc(newSignal) {\n      this.setTs = newSignal;\n    },\n    changeTs(ts) {\n      this.atTs = Number(ts);\n    },\n    handleStartScan(videoFile) {\n      // 处理开始扫描事件\n      console.log('开始扫描分析', videoFile);\n      console.log('当前任务配置:', this.$store.state.task_record.task_config);\n      console.log('任务ID:', this.$store.state.task_record.id);\n      console.log('任务模式:', this.$store.state.task_record.task_mode);\n      console.log('任务状态:', this.$store.state.task_record.task_status);\n      console.log('提示词:', this.$store.state.task_record.task_config.prompt);\n\n      // 检查提示词是否为空\n      if (!this.$store.state.task_record.task_config.prompt || this.$store.state.task_record.task_config.prompt.trim() === '') {\n        this.$message.error('请输入检测提示词');\n        return;\n      }\n\n      // 检查视频是否已上传\n      if (!this.$store.state.task_record.task_config.video_name) {\n        this.$message.error('请先上传视频文件');\n        return;\n      }\n\n      // 显示加载中消息\n      this.$message.loading('正在启动分析任务...', 2);\n\n      // 检查任务模式\n      if (this.$store.state.task_record.task_mode === 'balanced') {\n        // 显示请求详情\n        console.log('准备发送请求到 /RWScan，请求数据:', {\n          task_id: this.$store.state.task_record.id,\n          task_config: this.$store.state.task_record.task_config\n        });\n\n        // 显示axios配置\n        console.log('axios配置:', {\n          baseURL: this.$axios.defaults.baseURL,\n          headers: this.$axios.defaults.headers\n        });\n\n        // 调用后端API启动分析任务\n        this.$axios.post('/RWScan', {\n          task_id: this.$store.state.task_record.id,\n          task_config: this.$store.state.task_record.task_config\n        }, {\n          timeout: 10000,\n          // 设置超时时间为10秒\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        }).then(res => {\n          console.log('分析任务已启动，服务器响应:', res);\n          console.log('响应数据:', res.data);\n\n          // 更新任务状态为运行中\n          let updatedTask = {\n            ...this.$store.state.task_record,\n            task_status: 'running'\n          };\n          this.$store.dispatch('updateTaskRecord', updatedTask);\n          console.log('任务状态已更新为running');\n\n          // 立即跳转到任务列表页面，不显示多余的消息\n          console.log('准备跳转到任务列表页面');\n          this.shouldRender = false;\n          this.$router.push({\n            path: '/task'\n          });\n        }).catch(err => {\n          console.error('分析失败，错误详情:', err);\n          if (err.response) {\n            console.error('错误响应状态码:', err.response.status);\n            console.error('错误响应数据:', err.response.data);\n\n            // 显示详细错误信息\n            if (err.response.data && err.response.data.error) {\n              this.$message.error(`启动分析任务失败: ${err.response.data.error}`);\n            } else {\n              this.$message.error(`启动分析任务失败，状态码: ${err.response.status}`);\n            }\n          } else if (err.request) {\n            console.error('请求已发送但未收到响应');\n            this.$message.error('启动分析任务失败: 服务器未响应');\n          } else {\n            console.error('请求配置错误:', err.message);\n            this.$message.error(`启动分析任务失败: ${err.message}`);\n          }\n        });\n      } else {\n        this.$message.error('该模式尚在开发中');\n      }\n    },\n    logOut() {\n      this.shouldRender = false; // 设置 shouldRender 为 false，销毁组件\n      this.$router.push({\n        path: '/'\n      });\n    },\n    returnMain() {\n      this.shouldRender = false;\n      console.log(this.$store.state.task_record);\n      this.$router.push({\n        path: '/main'\n      });\n    },\n    returnUp() {\n      this.shouldRender = false;\n      this.$router.push({\n        path: '/task'\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["Inputs", "Results", "HomeFilled", "LogoutOutlined", "ArrowLeftOutlined", "name", "data", "shouldRender", "scan_signal", "setTs", "atTs", "components", "beforeRouteEnter", "_", "__", "next", "vm", "methods", "setTsFunc", "newSignal", "changeTs", "ts", "Number", "handleStartScan", "videoFile", "console", "log", "$store", "state", "task_record", "task_config", "id", "task_mode", "task_status", "prompt", "trim", "$message", "error", "video_name", "loading", "task_id", "baseURL", "$axios", "defaults", "headers", "post", "timeout", "then", "res", "updatedTask", "dispatch", "$router", "push", "path", "catch", "err", "response", "status", "request", "message", "logOut", "return<PERSON>ain", "returnUp"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\methods\\Task_MW.vue"], "sourcesContent": ["<template>\r\n  <div class=\"analysis-container\">\r\n    <div class=\"analysis-header\">\r\n      <div class=\"header-nav fade-in\">\r\n        <div class=\"nav-left\">\r\n          <div class=\"mw-logo-container\">\r\n            <img src=\"../assets/logo.svg\" alt=\"Logo\" class=\"mw-logo\" />\r\n          </div>\r\n          <a-button type=\"text\" @click=\"returnUp\" class=\"nav-button\">\r\n            <ArrowLeftOutlined />\r\n            返回任务列表\r\n          </a-button>\r\n          <a-button type=\"text\" @click=\"returnMain\" class=\"nav-button\">\r\n            <HomeFilled />\r\n            返回首页\r\n          </a-button>\r\n        </div>\r\n        <a-button type=\"text\" @click=\"logOut\" class=\"nav-button\">\r\n          <LogoutOutlined />\r\n          退出登录\r\n        </a-button>\r\n      </div>\r\n    </div>\r\n    <div class=\"analysis-content\">\r\n      <div class=\"inputs-column slide-in-left\">\r\n        <Inputs\r\n          v-if=\"shouldRender\"\r\n          :setTs=\"setTs\"\r\n          :atTs=\"atTs\"\r\n          :setTsFunc=\"setTsFunc\"\r\n          class=\"inputs-component\"\r\n          @start-scan=\"handleStartScan\"\r\n        />\r\n      </div>\r\n      <div class=\"results-column slide-in-right\">\r\n        <Results\r\n          v-if=\"shouldRender\"\r\n          :changeTs=\"changeTs\"\r\n          :setTsFunc=\"setTsFunc\"\r\n          class=\"results-component\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <!-- Loading overlays -->\r\n    <div v-if=\"$store.state.uploading\" class=\"overlay uploading-overlay\">\r\n      <div class=\"spinner\"></div>\r\n      <p class=\"overlay-text\">正在上传视频，请稍候...</p>\r\n    </div>\r\n\r\n    <div v-if=\"$store.state.task_ready\" class=\"overlay task-ready-overlay\">\r\n      <div class=\"spinner\"></div>\r\n      <p class=\"overlay-text\">正在准备任务数据，请稍候...</p>\r\n    </div>\r\n\r\n    <!-- 如果 $store.state.task_record.task_status等于“running”，显示遮罩-->\r\n    <div v-if=\"$store.state.task_record.task_status === 'running'\" class=\"taskRun-overlay\">\r\n        <div class=\"taskRun-spinner\"></div>\r\n        <p class=\"overlay-text\">任务运行中，请稍候...</p>\r\n        <p class=\"overlay-subtext\">分析完成后将自动显示结果</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Inputs from './MW/Inputs.vue';\r\nimport Results from './MW/Results.vue';\r\nimport { HomeFilled, LogoutOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';\r\n\r\nexport default {\r\n  name: 'MWTaskView',\r\n  data() {\r\n    return {\r\n      shouldRender: true,\r\n      // 视频操控\r\n      scan_signal: false,\r\n      setTs: false,\r\n      atTs: 0.0\r\n    };\r\n  },\r\n  components: {\r\n    Inputs,\r\n    Results,\r\n    HomeFilled,\r\n    LogoutOutlined,\r\n    ArrowLeftOutlined,\r\n  },\r\n  beforeRouteEnter(_, __, next) {\r\n    next((vm) => {\r\n      vm.shouldRender = true;\r\n    });\r\n  },\r\n  methods: {\r\n    setTsFunc(newSignal) {\r\n      this.setTs = newSignal;\r\n    },\r\n    changeTs(ts) {\r\n      this.atTs = Number(ts);\r\n    },\r\n    handleStartScan(videoFile) {\r\n      // 处理开始扫描事件\r\n      console.log('开始扫描分析', videoFile);\r\n      console.log('当前任务配置:', this.$store.state.task_record.task_config);\r\n      console.log('任务ID:', this.$store.state.task_record.id);\r\n      console.log('任务模式:', this.$store.state.task_record.task_mode);\r\n      console.log('任务状态:', this.$store.state.task_record.task_status);\r\n      console.log('提示词:', this.$store.state.task_record.task_config.prompt);\r\n\r\n      // 检查提示词是否为空\r\n      if (!this.$store.state.task_record.task_config.prompt || this.$store.state.task_record.task_config.prompt.trim() === '') {\r\n        this.$message.error('请输入检测提示词');\r\n        return;\r\n      }\r\n\r\n      // 检查视频是否已上传\r\n      if (!this.$store.state.task_record.task_config.video_name) {\r\n        this.$message.error('请先上传视频文件');\r\n        return;\r\n      }\r\n\r\n      // 显示加载中消息\r\n      this.$message.loading('正在启动分析任务...', 2);\r\n\r\n      // 检查任务模式\r\n      if (this.$store.state.task_record.task_mode === 'balanced') {\r\n        // 显示请求详情\r\n        console.log('准备发送请求到 /RWScan，请求数据:', {\r\n          task_id: this.$store.state.task_record.id,\r\n          task_config: this.$store.state.task_record.task_config\r\n        });\r\n\r\n        // 显示axios配置\r\n        console.log('axios配置:', {\r\n          baseURL: this.$axios.defaults.baseURL,\r\n          headers: this.$axios.defaults.headers\r\n        });\r\n\r\n        // 调用后端API启动分析任务\r\n        this.$axios.post('/RWScan', {\r\n          task_id: this.$store.state.task_record.id,\r\n          task_config: this.$store.state.task_record.task_config\r\n        }, {\r\n          timeout: 10000, // 设置超时时间为10秒\r\n          headers: {\r\n            'Content-Type': 'application/json'\r\n          }\r\n        })\r\n        .then((res) => {\r\n          console.log('分析任务已启动，服务器响应:', res);\r\n          console.log('响应数据:', res.data);\r\n\r\n          // 更新任务状态为运行中\r\n          let updatedTask = {...this.$store.state.task_record, task_status: 'running'};\r\n          this.$store.dispatch('updateTaskRecord', updatedTask);\r\n          console.log('任务状态已更新为running');\r\n\r\n          // 立即跳转到任务列表页面，不显示多余的消息\r\n          console.log('准备跳转到任务列表页面');\r\n          this.shouldRender = false;\r\n          this.$router.push({ path: '/task' });\r\n        })\r\n        .catch((err) => {\r\n          console.error('分析失败，错误详情:', err);\r\n\r\n          if (err.response) {\r\n            console.error('错误响应状态码:', err.response.status);\r\n            console.error('错误响应数据:', err.response.data);\r\n\r\n            // 显示详细错误信息\r\n            if (err.response.data && err.response.data.error) {\r\n              this.$message.error(`启动分析任务失败: ${err.response.data.error}`);\r\n            } else {\r\n              this.$message.error(`启动分析任务失败，状态码: ${err.response.status}`);\r\n            }\r\n          } else if (err.request) {\r\n            console.error('请求已发送但未收到响应');\r\n            this.$message.error('启动分析任务失败: 服务器未响应');\r\n          } else {\r\n            console.error('请求配置错误:', err.message);\r\n            this.$message.error(`启动分析任务失败: ${err.message}`);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.error('该模式尚在开发中');\r\n      }\r\n    },\r\n    logOut() {\r\n      this.shouldRender = false; // 设置 shouldRender 为 false，销毁组件\r\n      this.$router.push({ path: '/' });\r\n    },\r\n    returnMain() {\r\n      this.shouldRender = false;\r\n      console.log(this.$store.state.task_record);\r\n      this.$router.push({ path: '/main' });\r\n    },\r\n    returnUp() {\r\n      this.shouldRender = false;\r\n      this.$router.push({ path: '/task' });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n  <style scoped>\r\n.analysis-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n\r\n.analysis-header {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  padding: 15px 30px;\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);\r\n  border-bottom: 1px solid rgba(24, 144, 255, 0.2);\r\n  z-index: 10000;\r\n  position: relative;\r\n}\r\n\r\n.header-nav {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.nav-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.mw-logo-container {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.mw-logo {\r\n  width: 40px;\r\n  height: 40px;\r\n  object-fit: contain;\r\n  filter: drop-shadow(0 2px 8px rgba(24, 144, 255, 0.2));\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.mw-logo:hover {\r\n  transform: scale(1.1);\r\n  filter: drop-shadow(0 4px 12px rgba(24, 144, 255, 0.3));\r\n}\r\n\r\n.nav-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 15px;\r\n  color: #333;\r\n  transition: all 0.3s;\r\n  padding: 8px 16px;\r\n  border-radius: 8px;\r\n  background-color: rgba(255, 255, 255, 0.6);\r\n  border: 1px solid rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.nav-button:hover {\r\n  transform: translateY(-2px);\r\n  color: #1890ff;\r\n  background-color: rgba(255, 255, 255, 0.9);\r\n  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.analysis-content {\r\n  display: flex;\r\n  flex: 1;\r\n  padding: 20px;\r\n  gap: 20px;\r\n  min-height: calc(100vh - 80px);\r\n}\r\n\r\n.inputs-column, .results-column {\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 16px;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\r\n  overflow: auto; /* 改为auto允许内容滚动 */\r\n  border: 1px solid rgba(24, 144, 255, 0.1);\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.inputs-column::before, .results-column::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 5px;\r\n  height: 100%;\r\n  background-color: #1890ff;\r\n  opacity: 0.8;\r\n  z-index: 1;\r\n}\r\n\r\n.inputs-column {\r\n  width: 40%;\r\n  flex-direction: column;\r\n  min-height: calc(100vh - 120px);\r\n  max-height: calc(100vh - 120px);\r\n}\r\n\r\n.results-column {\r\n  width: 60%;\r\n  min-height: calc(100vh - 120px);\r\n  max-height: calc(100vh - 120px);\r\n}\r\n\r\n.inputs-component, .results-component {\r\n  height: 100%;\r\n  overflow: auto;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 10000;\r\n}\r\n\r\n.spinner {\r\n  width: 70px;\r\n  height: 70px;\r\n  border: 5px solid rgba(24, 144, 255, 0.1);\r\n  border-radius: 50%;\r\n  border-top-color: #1890ff;\r\n  animation: spin 1s ease-in-out infinite;\r\n  margin-bottom: 30px;\r\n  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.overlay-text {\r\n  color: #333;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  max-width: 80%;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  padding: 15px 30px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n  border: 1px solid rgba(24, 144, 255, 0.1);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.overlay-subtext {\r\n  color: #666;\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n  text-align: center;\r\n  max-width: 80%;\r\n  background: rgba(255, 255, 255, 0.6);\r\n  padding: 10px 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);\r\n  border: 1px solid rgba(24, 144, 255, 0.05);\r\n}\r\n\r\n.taskRun-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 10000;\r\n}\r\n\r\n.taskRun-spinner {\r\n  width: 70px;\r\n  height: 70px;\r\n  border: 5px solid rgba(24, 144, 255, 0.1);\r\n  border-radius: 50%;\r\n  border-top-color: #1890ff;\r\n  animation: spin 1s ease-in-out infinite;\r\n  margin-bottom: 30px;\r\n  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n@keyframes spin {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* 视频控制按钮隐藏 */\r\n.video::-webkit-media-controls-fullscreen-button {\r\n  display: none !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .analysis-content {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .inputs-column, .results-column {\r\n    width: 100%;\r\n  }\r\n\r\n  .inputs-column {\r\n    min-height: 50vh;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .results-column {\r\n    min-height: 50vh;\r\n  }\r\n}\r\n  </style>\r\n"], "mappings": ";AAgEA,OAAOA,MAAK,MAAO,iBAAiB;AACpC,OAAOC,OAAM,MAAO,kBAAkB;AACtC,SAASC,UAAU,EAAEC,cAAc,EAAEC,iBAAgB,QAAS,uBAAuB;AAErF,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,IAAI;MAClB;MACAC,WAAW,EAAE,KAAK;MAClBC,KAAK,EAAE,KAAK;MACZC,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDC,UAAU,EAAE;IACVX,MAAM;IACNC,OAAO;IACPC,UAAU;IACVC,cAAc;IACdC;EACF,CAAC;EACDQ,gBAAgBA,CAACC,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAE;IAC5BA,IAAI,CAAEC,EAAE,IAAK;MACXA,EAAE,CAACT,YAAW,GAAI,IAAI;IACxB,CAAC,CAAC;EACJ,CAAC;EACDU,OAAO,EAAE;IACPC,SAASA,CAACC,SAAS,EAAE;MACnB,IAAI,CAACV,KAAI,GAAIU,SAAS;IACxB,CAAC;IACDC,QAAQA,CAACC,EAAE,EAAE;MACX,IAAI,CAACX,IAAG,GAAIY,MAAM,CAACD,EAAE,CAAC;IACxB,CAAC;IACDE,eAAeA,CAACC,SAAS,EAAE;MACzB;MACAC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,SAAS,CAAC;MAChCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,WAAW,CAACC,WAAW,CAAC;MACjEL,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,WAAW,CAACE,EAAE,CAAC;MACtDN,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,WAAW,CAACG,SAAS,CAAC;MAC7DP,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,WAAW,CAACI,WAAW,CAAC;MAC/DR,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,WAAW,CAACC,WAAW,CAACI,MAAM,CAAC;;MAErE;MACA,IAAI,CAAC,IAAI,CAACP,MAAM,CAACC,KAAK,CAACC,WAAW,CAACC,WAAW,CAACI,MAAK,IAAK,IAAI,CAACP,MAAM,CAACC,KAAK,CAACC,WAAW,CAACC,WAAW,CAACI,MAAM,CAACC,IAAI,CAAC,MAAM,EAAE,EAAE;QACvH,IAAI,CAACC,QAAQ,CAACC,KAAK,CAAC,UAAU,CAAC;QAC/B;MACF;;MAEA;MACA,IAAI,CAAC,IAAI,CAACV,MAAM,CAACC,KAAK,CAACC,WAAW,CAACC,WAAW,CAACQ,UAAU,EAAE;QACzD,IAAI,CAACF,QAAQ,CAACC,KAAK,CAAC,UAAU,CAAC;QAC/B;MACF;;MAEA;MACA,IAAI,CAACD,QAAQ,CAACG,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;;MAEvC;MACA,IAAI,IAAI,CAACZ,MAAM,CAACC,KAAK,CAACC,WAAW,CAACG,SAAQ,KAAM,UAAU,EAAE;QAC1D;QACAP,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnCc,OAAO,EAAE,IAAI,CAACb,MAAM,CAACC,KAAK,CAACC,WAAW,CAACE,EAAE;UACzCD,WAAW,EAAE,IAAI,CAACH,MAAM,CAACC,KAAK,CAACC,WAAW,CAACC;QAC7C,CAAC,CAAC;;QAEF;QACAL,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;UACtBe,OAAO,EAAE,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACF,OAAO;UACrCG,OAAO,EAAE,IAAI,CAACF,MAAM,CAACC,QAAQ,CAACC;QAChC,CAAC,CAAC;;QAEF;QACA,IAAI,CAACF,MAAM,CAACG,IAAI,CAAC,SAAS,EAAE;UAC1BL,OAAO,EAAE,IAAI,CAACb,MAAM,CAACC,KAAK,CAACC,WAAW,CAACE,EAAE;UACzCD,WAAW,EAAE,IAAI,CAACH,MAAM,CAACC,KAAK,CAACC,WAAW,CAACC;QAC7C,CAAC,EAAE;UACDgB,OAAO,EAAE,KAAK;UAAE;UAChBF,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,EACAG,IAAI,CAAEC,GAAG,IAAK;UACbvB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEsB,GAAG,CAAC;UAClCvB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEsB,GAAG,CAAC1C,IAAI,CAAC;;UAE9B;UACA,IAAI2C,WAAU,GAAI;YAAC,GAAG,IAAI,CAACtB,MAAM,CAACC,KAAK,CAACC,WAAW;YAAEI,WAAW,EAAE;UAAS,CAAC;UAC5E,IAAI,CAACN,MAAM,CAACuB,QAAQ,CAAC,kBAAkB,EAAED,WAAW,CAAC;UACrDxB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;UAE9B;UACAD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;UAC1B,IAAI,CAACnB,YAAW,GAAI,KAAK;UACzB,IAAI,CAAC4C,OAAO,CAACC,IAAI,CAAC;YAAEC,IAAI,EAAE;UAAQ,CAAC,CAAC;QACtC,CAAC,EACAC,KAAK,CAAEC,GAAG,IAAK;UACd9B,OAAO,CAACY,KAAK,CAAC,YAAY,EAAEkB,GAAG,CAAC;UAEhC,IAAIA,GAAG,CAACC,QAAQ,EAAE;YAChB/B,OAAO,CAACY,KAAK,CAAC,UAAU,EAAEkB,GAAG,CAACC,QAAQ,CAACC,MAAM,CAAC;YAC9ChC,OAAO,CAACY,KAAK,CAAC,SAAS,EAAEkB,GAAG,CAACC,QAAQ,CAAClD,IAAI,CAAC;;YAE3C;YACA,IAAIiD,GAAG,CAACC,QAAQ,CAAClD,IAAG,IAAKiD,GAAG,CAACC,QAAQ,CAAClD,IAAI,CAAC+B,KAAK,EAAE;cAChD,IAAI,CAACD,QAAQ,CAACC,KAAK,CAAE,aAAYkB,GAAG,CAACC,QAAQ,CAAClD,IAAI,CAAC+B,KAAM,EAAC,CAAC;YAC7D,OAAO;cACL,IAAI,CAACD,QAAQ,CAACC,KAAK,CAAE,iBAAgBkB,GAAG,CAACC,QAAQ,CAACC,MAAO,EAAC,CAAC;YAC7D;UACF,OAAO,IAAIF,GAAG,CAACG,OAAO,EAAE;YACtBjC,OAAO,CAACY,KAAK,CAAC,aAAa,CAAC;YAC5B,IAAI,CAACD,QAAQ,CAACC,KAAK,CAAC,kBAAkB,CAAC;UACzC,OAAO;YACLZ,OAAO,CAACY,KAAK,CAAC,SAAS,EAAEkB,GAAG,CAACI,OAAO,CAAC;YACrC,IAAI,CAACvB,QAAQ,CAACC,KAAK,CAAE,aAAYkB,GAAG,CAACI,OAAQ,EAAC,CAAC;UACjD;QACF,CAAC,CAAC;MACJ,OAAO;QACL,IAAI,CAACvB,QAAQ,CAACC,KAAK,CAAC,UAAU,CAAC;MACjC;IACF,CAAC;IACDuB,MAAMA,CAAA,EAAG;MACP,IAAI,CAACrD,YAAW,GAAI,KAAK,EAAE;MAC3B,IAAI,CAAC4C,OAAO,CAACC,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAI,CAAC,CAAC;IAClC,CAAC;IACDQ,UAAUA,CAAA,EAAG;MACX,IAAI,CAACtD,YAAW,GAAI,KAAK;MACzBkB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,WAAW,CAAC;MAC1C,IAAI,CAACsB,OAAO,CAACC,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAQ,CAAC,CAAC;IACtC,CAAC;IACDS,QAAQA,CAAA,EAAG;MACT,IAAI,CAACvD,YAAW,GAAI,KAAK;MACzB,IAAI,CAAC4C,OAAO,CAACC,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAQ,CAAC,CAAC;IACtC;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}