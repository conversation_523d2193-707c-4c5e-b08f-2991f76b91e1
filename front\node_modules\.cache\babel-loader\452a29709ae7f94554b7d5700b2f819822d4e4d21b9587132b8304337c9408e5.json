{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-533b7a44\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"analysis-container\"\n};\nconst _hoisted_2 = {\n  class: \"analysis-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-nav fade-in\"\n};\nconst _hoisted_4 = {\n  class: \"analysis-content\"\n};\nconst _hoisted_5 = {\n  class: \"inputs-column slide-in-left\"\n};\nconst _hoisted_6 = {\n  class: \"results-column slide-in-right\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"overlay uploading-overlay\"\n};\nconst _hoisted_8 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"spinner\"\n}, null, -1 /* HOISTED */));\nconst _hoisted_9 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"p\", {\n  class: \"overlay-text\"\n}, \"正在上传视频，请稍候...\", -1 /* HOISTED */));\nconst _hoisted_10 = [_hoisted_8, _hoisted_9];\nconst _hoisted_11 = {\n  key: 1,\n  class: \"overlay task-ready-overlay\"\n};\nconst _hoisted_12 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"spinner\"\n}, null, -1 /* HOISTED */));\nconst _hoisted_13 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"p\", {\n  class: \"overlay-text\"\n}, \"正在准备任务数据，请稍候...\", -1 /* HOISTED */));\nconst _hoisted_14 = [_hoisted_12, _hoisted_13];\nconst _hoisted_15 = {\n  key: 2,\n  class: \"taskRun-overlay\"\n};\nconst _hoisted_16 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"taskRun-spinner\"\n}, null, -1 /* HOISTED */));\nconst _hoisted_17 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"p\", {\n  class: \"overlay-text\"\n}, \"任务运行中，请稍候...\", -1 /* HOISTED */));\nconst _hoisted_18 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"p\", {\n  class: \"overlay-subtext\"\n}, \"分析完成后将自动显示结果\", -1 /* HOISTED */));\nconst _hoisted_19 = [_hoisted_16, _hoisted_17, _hoisted_18];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ArrowLeftOutlined = _resolveComponent(\"ArrowLeftOutlined\");\n  const _component_a_button = _resolveComponent(\"a-button\");\n  const _component_HomeFilled = _resolveComponent(\"HomeFilled\");\n  const _component_LogoutOutlined = _resolveComponent(\"LogoutOutlined\");\n  const _component_Inputs = _resolveComponent(\"Inputs\");\n  const _component_Results = _resolveComponent(\"Results\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_a_button, {\n    type: \"text\",\n    onClick: $options.returnUp,\n    class: \"nav-button\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_ArrowLeftOutlined), _createTextVNode(\" 返回任务列表 \")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_a_button, {\n    type: \"text\",\n    onClick: $options.returnMain,\n    class: \"nav-button\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_HomeFilled), _createTextVNode(\" 返回首页 \")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_a_button, {\n    type: \"text\",\n    onClick: $options.logOut,\n    class: \"nav-button\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_LogoutOutlined), _createTextVNode(\" 退出登录 \")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [$data.shouldRender ? (_openBlock(), _createBlock(_component_Inputs, {\n    key: 0,\n    setTs: $data.setTs,\n    atTs: $data.atTs,\n    setTsFunc: $options.setTsFunc,\n    class: \"inputs-component\",\n    onStartScan: $options.handleStartScan\n  }, null, 8 /* PROPS */, [\"setTs\", \"atTs\", \"setTsFunc\", \"onStartScan\"])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_6, [$data.shouldRender ? (_openBlock(), _createBlock(_component_Results, {\n    key: 0,\n    changeTs: $options.changeTs,\n    setTsFunc: $options.setTsFunc,\n    class: \"results-component\"\n  }, null, 8 /* PROPS */, [\"changeTs\", \"setTsFunc\"])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Loading overlays \"), _ctx.$store.state.uploading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [..._hoisted_10])) : _createCommentVNode(\"v-if\", true), _ctx.$store.state.task_ready ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [..._hoisted_14])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 如果 $store.state.task_record.task_status等于“running”，显示遮罩\"), _ctx.$store.state.task_record.task_status === 'running' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [..._hoisted_19])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementVNode", "_hoisted_8", "_hoisted_9", "_hoisted_12", "_hoisted_13", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_a_button", "type", "onClick", "$options", "returnUp", "default", "_withCtx", "_component_ArrowLeftOutlined", "_createTextVNode", "_", "return<PERSON>ain", "_component_HomeFilled", "logOut", "_component_LogoutOutlined", "_hoisted_4", "_hoisted_5", "$data", "shouldRender", "_createBlock", "_component_Inputs", "setTs", "atTs", "setTsFunc", "onStartScan", "handleStartScan", "_createCommentVNode", "_hoisted_6", "_component_Results", "changeTs", "_ctx", "$store", "state", "uploading", "_hoisted_7", "_hoisted_10", "task_ready", "_hoisted_11", "_hoisted_14", "task_record", "task_status", "_hoisted_15", "_hoisted_19"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\methods\\Task_MW.vue"], "sourcesContent": ["<template>\r\n  <div class=\"analysis-container\">\r\n    <div class=\"analysis-header\">\r\n      <div class=\"header-nav fade-in\">\r\n        <a-button type=\"text\" @click=\"returnUp\" class=\"nav-button\">\r\n          <ArrowLeftOutlined />\r\n          返回任务列表\r\n        </a-button>\r\n        <a-button type=\"text\" @click=\"returnMain\" class=\"nav-button\">\r\n          <HomeFilled />\r\n          返回首页\r\n        </a-button>\r\n        <a-button type=\"text\" @click=\"logOut\" class=\"nav-button\">\r\n          <LogoutOutlined />\r\n          退出登录\r\n        </a-button>\r\n      </div>\r\n    </div>\r\n    <div class=\"analysis-content\">\r\n      <div class=\"inputs-column slide-in-left\">\r\n        <Inputs\r\n          v-if=\"shouldRender\"\r\n          :setTs=\"setTs\"\r\n          :atTs=\"atTs\"\r\n          :setTsFunc=\"setTsFunc\"\r\n          class=\"inputs-component\"\r\n          @start-scan=\"handleStartScan\"\r\n        />\r\n      </div>\r\n      <div class=\"results-column slide-in-right\">\r\n        <Results\r\n          v-if=\"shouldRender\"\r\n          :changeTs=\"changeTs\"\r\n          :setTsFunc=\"setTsFunc\"\r\n          class=\"results-component\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <!-- Loading overlays -->\r\n    <div v-if=\"$store.state.uploading\" class=\"overlay uploading-overlay\">\r\n      <div class=\"spinner\"></div>\r\n      <p class=\"overlay-text\">正在上传视频，请稍候...</p>\r\n    </div>\r\n\r\n    <div v-if=\"$store.state.task_ready\" class=\"overlay task-ready-overlay\">\r\n      <div class=\"spinner\"></div>\r\n      <p class=\"overlay-text\">正在准备任务数据，请稍候...</p>\r\n    </div>\r\n\r\n    <!-- 如果 $store.state.task_record.task_status等于“running”，显示遮罩-->\r\n    <div v-if=\"$store.state.task_record.task_status === 'running'\" class=\"taskRun-overlay\">\r\n        <div class=\"taskRun-spinner\"></div>\r\n        <p class=\"overlay-text\">任务运行中，请稍候...</p>\r\n        <p class=\"overlay-subtext\">分析完成后将自动显示结果</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Inputs from './MW/Inputs.vue';\r\nimport Results from './MW/Results.vue';\r\nimport { HomeFilled, LogoutOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';\r\n\r\nexport default {\r\n  name: 'MWTaskView',\r\n  data() {\r\n    return {\r\n      shouldRender: true,\r\n      // 视频操控\r\n      scan_signal: false,\r\n      setTs: false,\r\n      atTs: 0.0\r\n    };\r\n  },\r\n  components: {\r\n    Inputs,\r\n    Results,\r\n    HomeFilled,\r\n    LogoutOutlined,\r\n    ArrowLeftOutlined,\r\n  },\r\n  beforeRouteEnter(_, __, next) {\r\n    next((vm) => {\r\n      vm.shouldRender = true;\r\n    });\r\n  },\r\n  methods: {\r\n    setTsFunc(newSignal) {\r\n      this.setTs = newSignal;\r\n    },\r\n    changeTs(ts) {\r\n      this.atTs = Number(ts);\r\n    },\r\n    handleStartScan(videoFile) {\r\n      // 处理开始扫描事件\r\n      console.log('开始扫描分析', videoFile);\r\n      console.log('当前任务配置:', this.$store.state.task_record.task_config);\r\n      console.log('任务ID:', this.$store.state.task_record.id);\r\n      console.log('任务模式:', this.$store.state.task_record.task_mode);\r\n      console.log('任务状态:', this.$store.state.task_record.task_status);\r\n      console.log('提示词:', this.$store.state.task_record.task_config.prompt);\r\n\r\n      // 检查提示词是否为空\r\n      if (!this.$store.state.task_record.task_config.prompt || this.$store.state.task_record.task_config.prompt.trim() === '') {\r\n        this.$message.error('请输入检测提示词');\r\n        return;\r\n      }\r\n\r\n      // 检查视频是否已上传\r\n      if (!this.$store.state.task_record.task_config.video_name) {\r\n        this.$message.error('请先上传视频文件');\r\n        return;\r\n      }\r\n\r\n      // 显示加载中消息\r\n      this.$message.loading('正在启动分析任务...', 2);\r\n\r\n      // 检查任务模式\r\n      if (this.$store.state.task_record.task_mode === 'balanced') {\r\n        // 显示请求详情\r\n        console.log('准备发送请求到 /RWScan，请求数据:', {\r\n          task_id: this.$store.state.task_record.id,\r\n          task_config: this.$store.state.task_record.task_config\r\n        });\r\n\r\n        // 显示axios配置\r\n        console.log('axios配置:', {\r\n          baseURL: this.$axios.defaults.baseURL,\r\n          headers: this.$axios.defaults.headers\r\n        });\r\n\r\n        // 调用后端API启动分析任务\r\n        this.$axios.post('/RWScan', {\r\n          task_id: this.$store.state.task_record.id,\r\n          task_config: this.$store.state.task_record.task_config\r\n        }, {\r\n          timeout: 10000, // 设置超时时间为10秒\r\n          headers: {\r\n            'Content-Type': 'application/json'\r\n          }\r\n        })\r\n        .then((res) => {\r\n          console.log('分析任务已启动，服务器响应:', res);\r\n          console.log('响应数据:', res.data);\r\n\r\n          // 更新任务状态为运行中\r\n          let updatedTask = {...this.$store.state.task_record, task_status: 'running'};\r\n          this.$store.dispatch('updateTaskRecord', updatedTask);\r\n          console.log('任务状态已更新为running');\r\n\r\n          // 立即跳转到任务列表页面，不显示多余的消息\r\n          console.log('准备跳转到任务列表页面');\r\n          this.shouldRender = false;\r\n          this.$router.push({ path: '/task' });\r\n        })\r\n        .catch((err) => {\r\n          console.error('分析失败，错误详情:', err);\r\n\r\n          if (err.response) {\r\n            console.error('错误响应状态码:', err.response.status);\r\n            console.error('错误响应数据:', err.response.data);\r\n\r\n            // 显示详细错误信息\r\n            if (err.response.data && err.response.data.error) {\r\n              this.$message.error(`启动分析任务失败: ${err.response.data.error}`);\r\n            } else {\r\n              this.$message.error(`启动分析任务失败，状态码: ${err.response.status}`);\r\n            }\r\n          } else if (err.request) {\r\n            console.error('请求已发送但未收到响应');\r\n            this.$message.error('启动分析任务失败: 服务器未响应');\r\n          } else {\r\n            console.error('请求配置错误:', err.message);\r\n            this.$message.error(`启动分析任务失败: ${err.message}`);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.error('该模式尚在开发中');\r\n      }\r\n    },\r\n    logOut() {\r\n      this.shouldRender = false; // 设置 shouldRender 为 false，销毁组件\r\n      this.$router.push({ path: '/' });\r\n    },\r\n    returnMain() {\r\n      this.shouldRender = false;\r\n      console.log(this.$store.state.task_record);\r\n      this.$router.push({ path: '/main' });\r\n    },\r\n    returnUp() {\r\n      this.shouldRender = false;\r\n      this.$router.push({ path: '/task' });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n  <style scoped>\r\n.analysis-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n\r\n.analysis-header {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  padding: 15px 30px;\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);\r\n  border-bottom: 1px solid rgba(24, 144, 255, 0.2);\r\n  z-index: 10000;\r\n  position: relative;\r\n}\r\n\r\n.header-nav {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.nav-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.mw-logo-container {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.mw-logo {\r\n  width: 40px;\r\n  height: 40px;\r\n  object-fit: contain;\r\n  filter: drop-shadow(0 2px 8px rgba(24, 144, 255, 0.2));\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.mw-logo:hover {\r\n  transform: scale(1.1);\r\n  filter: drop-shadow(0 4px 12px rgba(24, 144, 255, 0.3));\r\n}\r\n\r\n.nav-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 15px;\r\n  color: #333;\r\n  transition: all 0.3s;\r\n  padding: 8px 16px;\r\n  border-radius: 8px;\r\n  background-color: rgba(255, 255, 255, 0.6);\r\n  border: 1px solid rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.nav-button:hover {\r\n  transform: translateY(-2px);\r\n  color: #1890ff;\r\n  background-color: rgba(255, 255, 255, 0.9);\r\n  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.analysis-content {\r\n  display: flex;\r\n  flex: 1;\r\n  padding: 20px;\r\n  gap: 20px;\r\n  min-height: calc(100vh - 80px);\r\n}\r\n\r\n.inputs-column, .results-column {\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 16px;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\r\n  overflow: auto; /* 改为auto允许内容滚动 */\r\n  border: 1px solid rgba(24, 144, 255, 0.1);\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.inputs-column::before, .results-column::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 5px;\r\n  height: 100%;\r\n  background-color: #1890ff;\r\n  opacity: 0.8;\r\n  z-index: 1;\r\n}\r\n\r\n.inputs-column {\r\n  width: 40%;\r\n  flex-direction: column;\r\n  min-height: calc(100vh - 120px);\r\n  max-height: calc(100vh - 120px);\r\n}\r\n\r\n.results-column {\r\n  width: 60%;\r\n  min-height: calc(100vh - 120px);\r\n  max-height: calc(100vh - 120px);\r\n}\r\n\r\n.inputs-component, .results-component {\r\n  height: 100%;\r\n  overflow: auto;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 10000;\r\n}\r\n\r\n.spinner {\r\n  width: 70px;\r\n  height: 70px;\r\n  border: 5px solid rgba(24, 144, 255, 0.1);\r\n  border-radius: 50%;\r\n  border-top-color: #1890ff;\r\n  animation: spin 1s ease-in-out infinite;\r\n  margin-bottom: 30px;\r\n  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.overlay-text {\r\n  color: #333;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  max-width: 80%;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  padding: 15px 30px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n  border: 1px solid rgba(24, 144, 255, 0.1);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.overlay-subtext {\r\n  color: #666;\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n  text-align: center;\r\n  max-width: 80%;\r\n  background: rgba(255, 255, 255, 0.6);\r\n  padding: 10px 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);\r\n  border: 1px solid rgba(24, 144, 255, 0.05);\r\n}\r\n\r\n.taskRun-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 10000;\r\n}\r\n\r\n.taskRun-spinner {\r\n  width: 70px;\r\n  height: 70px;\r\n  border: 5px solid rgba(24, 144, 255, 0.1);\r\n  border-radius: 50%;\r\n  border-top-color: #1890ff;\r\n  animation: spin 1s ease-in-out infinite;\r\n  margin-bottom: 30px;\r\n  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n@keyframes spin {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* 视频控制按钮隐藏 */\r\n.video::-webkit-media-controls-fullscreen-button {\r\n  display: none !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .analysis-content {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .inputs-column, .results-column {\r\n    width: 100%;\r\n  }\r\n\r\n  .inputs-column {\r\n    min-height: 50vh;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .results-column {\r\n    min-height: 50vh;\r\n  }\r\n}\r\n  </style>\r\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAoB;;EAe5BA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAA6B;;EAUnCA,KAAK,EAAC;AAA+B;;EA7BhDC,GAAA;EAuCuCD,KAAK,EAAC;;gEACvCE,mBAAA,CAA2B;EAAtBF,KAAK,EAAC;AAAS;gEACpBE,mBAAA,CAAyC;EAAtCF,KAAK,EAAC;AAAc,GAAC,eAAa;qBADrCG,UAA2B,EAC3BC,UAAyC,C;;EAzC/CH,GAAA;EA4CwCD,KAAK,EAAC;;iEACxCE,mBAAA,CAA2B;EAAtBF,KAAK,EAAC;AAAS;iEACpBE,mBAAA,CAA2C;EAAxCF,KAAK,EAAC;AAAc,GAAC,iBAAe;qBADvCK,WAA2B,EAC3BC,WAA2C,C;;EA9CjDL,GAAA;EAkDmED,KAAK,EAAC;;iEACjEE,mBAAA,CAAmC;EAA9BF,KAAK,EAAC;AAAiB;iEAC5BE,mBAAA,CAAwC;EAArCF,KAAK,EAAC;AAAc,GAAC,cAAY;iEACpCE,mBAAA,CAA2C;EAAxCF,KAAK,EAAC;AAAiB,GAAC,cAAY;qBAFvCO,WAAmC,EACnCC,WAAwC,EACxCC,WAA2C,C;;;;;;;;uBApDjDC,mBAAA,CAsDM,OAtDNC,UAsDM,GArDJT,mBAAA,CAeM,OAfNU,UAeM,GAdJV,mBAAA,CAaM,OAbNW,UAaM,GAZJC,YAAA,CAGWC,mBAAA;IAHDC,IAAI,EAAC,MAAM;IAAEC,OAAK,EAAEC,QAAA,CAAAC,QAAQ;IAAEnB,KAAK,EAAC;;IAJtDoB,OAAA,EAAAC,QAAA,CAKU,MAAqB,CAArBP,YAAA,CAAqBQ,4BAAA,GAL/BC,gBAAA,CAK+B,UAEvB,E;IAPRC,CAAA;kCAQQV,YAAA,CAGWC,mBAAA;IAHDC,IAAI,EAAC,MAAM;IAAEC,OAAK,EAAEC,QAAA,CAAAO,UAAU;IAAEzB,KAAK,EAAC;;IARxDoB,OAAA,EAAAC,QAAA,CASU,MAAc,CAAdP,YAAA,CAAcY,qBAAA,GATxBH,gBAAA,CASwB,QAEhB,E;IAXRC,CAAA;kCAYQV,YAAA,CAGWC,mBAAA;IAHDC,IAAI,EAAC,MAAM;IAAEC,OAAK,EAAEC,QAAA,CAAAS,MAAM;IAAE3B,KAAK,EAAC;;IAZpDoB,OAAA,EAAAC,QAAA,CAaU,MAAkB,CAAlBP,YAAA,CAAkBc,yBAAA,GAb5BL,gBAAA,CAa4B,QAEpB,E;IAfRC,CAAA;sCAkBItB,mBAAA,CAmBM,OAnBN2B,UAmBM,GAlBJ3B,mBAAA,CASM,OATN4B,UASM,GAPIC,KAAA,CAAAC,YAAY,I,cADpBC,YAAA,CAOEC,iBAAA;IA3BVjC,GAAA;IAsBWkC,KAAK,EAAEJ,KAAA,CAAAI,KAAK;IACZC,IAAI,EAAEL,KAAA,CAAAK,IAAI;IACVC,SAAS,EAAEnB,QAAA,CAAAmB,SAAS;IACrBrC,KAAK,EAAC,kBAAkB;IACvBsC,WAAU,EAAEpB,QAAA,CAAAqB;4EA1BvBC,mBAAA,e,GA6BMtC,mBAAA,CAOM,OAPNuC,UAOM,GALIV,KAAA,CAAAC,YAAY,I,cADpBC,YAAA,CAKES,kBAAA;IAnCVzC,GAAA;IAgCW0C,QAAQ,EAAEzB,QAAA,CAAAyB,QAAQ;IAClBN,SAAS,EAAEnB,QAAA,CAAAmB,SAAS;IACrBrC,KAAK,EAAC;wDAlChBwC,mBAAA,e,KAsCIA,mBAAA,sBAAyB,EACdI,IAAA,CAAAC,MAAM,CAACC,KAAK,CAACC,SAAS,I,cAAjCrC,mBAAA,CAGM,OAHNsC,UAGM,EA1CV,IAAAC,WAAA,MAAAT,mBAAA,gBA4CeI,IAAA,CAAAC,MAAM,CAACC,KAAK,CAACI,UAAU,I,cAAlCxC,mBAAA,CAGM,OAHNyC,WAGM,EA/CV,IAAAC,WAAA,MAAAZ,mBAAA,gBAiDIA,mBAAA,4DAA+D,EACpDI,IAAA,CAAAC,MAAM,CAACC,KAAK,CAACO,WAAW,CAACC,WAAW,kB,cAA/C5C,mBAAA,CAIM,OAJN6C,WAIM,EAtDV,IAAAC,WAAA,MAAAhB,mBAAA,e"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}