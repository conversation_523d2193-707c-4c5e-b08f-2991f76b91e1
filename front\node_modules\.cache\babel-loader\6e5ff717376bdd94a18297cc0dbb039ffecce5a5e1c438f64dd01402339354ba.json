{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport UserInfo from '../components/user_info.vue';\nimport UserChangePassword from '../components/user_ch_pwd.vue';\nimport UserChangeEmail from '../components/user_ch_mail.vue';\nimport UserAdmin from '../components/user_admin.vue';\nimport { LoginOutlined, HomeFilled, TeamOutlined, MailOutlined, IdcardOutlined, InsuranceOutlined } from '@ant-design/icons-vue';\nexport default {\n  name: 'UserView',\n  data() {\n    return {\n      currentComponent: 'UserInfo'\n    };\n  },\n  methods: {\n    changeComponent(componentName) {\n      this.currentComponent = componentName;\n    },\n    goToLogin() {\n      this.currentComponent = 'UserInfo';\n      this.$router.push('/');\n    },\n    goToMain() {\n      this.currentComponent = 'UserInfo';\n      this.$router.push('/main');\n    },\n    getAvatarText() {\n      // 获取用户名的首字母或前两个字符作为头像文本\n      const username = this.$store.state.username;\n      if (!username) return '?';\n\n      // 如果用户名是中文，取第一个字符\n      if (/[\\u4e00-\\u9fa5]/.test(username)) {\n        return username.substring(0, 1);\n      }\n\n      // 如果是英文，取首字母大写\n      return username.substring(0, 1).toUpperCase();\n    }\n  },\n  components: {\n    UserInfo,\n    UserChangePassword,\n    UserChangeEmail,\n    IdcardOutlined,\n    TeamOutlined,\n    UserAdmin,\n    LoginOutlined,\n    HomeFilled,\n    MailOutlined,\n    InsuranceOutlined\n  }\n};", "map": {"version": 3, "names": ["UserInfo", "UserChangePassword", "UserChangeEmail", "UserAdmin", "LoginOutlined", "HomeFilled", "TeamOutlined", "MailOutlined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "InsuranceOutlined", "name", "data", "currentComponent", "methods", "changeComponent", "componentName", "goToLogin", "$router", "push", "goToMain", "getAvatarText", "username", "$store", "state", "test", "substring", "toUpperCase", "components"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\pages\\User.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-view-container\">\r\n    <div class=\"sidebar fade-in\">\r\n      <div class=\"user-profile\">\r\n        <div class=\"user-logo-container\">\r\n          <img src=\"../assets/logo.svg\" alt=\"Logo\" class=\"user-logo\" />\r\n        </div>\r\n        <div class=\"avatar-container\">\r\n          <a-avatar :size=\"80\" class=\"user-avatar\">\r\n            <template #icon>\r\n              <span class=\"avatar-text\">{{ getAvatarText() }}</span>\r\n            </template>\r\n          </a-avatar>\r\n          <div class=\"avatar-status\"></div>\r\n        </div>\r\n        <h2 class=\"username\">{{ $store.state.username }}</h2>\r\n        <p class=\"user-role\">{{ $store.state.is_admin ? '管理员' : '普通用户' }}</p>\r\n      </div>\r\n\r\n      <div class=\"menu-items\">\r\n        <a-button\r\n          @click=\"changeComponent('UserInfo')\"\r\n          :class=\"['menu-item', { 'selected': currentComponent === 'UserInfo' }]\"\r\n          type=\"text\"\r\n        >\r\n          <IdcardOutlined />\r\n          <span>个人信息</span>\r\n        </a-button>\r\n\r\n        <a-button\r\n          @click=\"changeComponent('UserChangePassword')\"\r\n          :class=\"['menu-item', { 'selected': currentComponent === 'UserChangePassword' }]\"\r\n          type=\"text\"\r\n        >\r\n          <InsuranceOutlined />\r\n          <span>修改密码</span>\r\n        </a-button>\r\n\r\n        <a-button\r\n          @click=\"changeComponent('UserChangeEmail')\"\r\n          :class=\"['menu-item', { 'selected': currentComponent === 'UserChangeEmail' }]\"\r\n          type=\"text\"\r\n        >\r\n          <MailOutlined />\r\n          <span>修改邮箱</span>\r\n        </a-button>\r\n\r\n        <a-button\r\n          v-if=\"$store.state.is_admin\"\r\n          @click=\"changeComponent('UserAdmin')\"\r\n          :class=\"['menu-item', { 'selected': currentComponent === 'UserAdmin' }]\"\r\n          type=\"text\"\r\n        >\r\n          <TeamOutlined />\r\n          <span>用户管理</span>\r\n        </a-button>\r\n      </div>\r\n\r\n      <div class=\"bottom-actions\">\r\n        <a-button @click=\"goToMain\" type=\"primary\" class=\"action-button home-button\">\r\n          <HomeFilled />\r\n          <span>返回首页</span>\r\n        </a-button>\r\n\r\n        <a-button @click=\"goToLogin\" type=\"danger\" class=\"action-button logout-button\">\r\n          <LoginOutlined />\r\n          <span>退出登录</span>\r\n        </a-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"content-area slide-in-right\">\r\n      <div class=\"content-header\">\r\n        <h1 class=\"page-title\">\r\n          <span v-if=\"currentComponent === 'UserInfo'\">个人信息</span>\r\n          <span v-else-if=\"currentComponent === 'UserChangePassword'\">修改密码</span>\r\n          <span v-else-if=\"currentComponent === 'UserChangeEmail'\">修改邮箱</span>\r\n          <span v-else-if=\"currentComponent === 'UserAdmin'\">用户管理</span>\r\n        </h1>\r\n      </div>\r\n\r\n      <div class=\"content-body\">\r\n        <component :is=\"currentComponent\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserInfo from '../components/user_info.vue';\r\nimport UserChangePassword from '../components/user_ch_pwd.vue';\r\nimport UserChangeEmail from '../components/user_ch_mail.vue';\r\nimport UserAdmin from '../components/user_admin.vue';\r\nimport { LoginOutlined, HomeFilled, TeamOutlined, MailOutlined, IdcardOutlined, InsuranceOutlined} from '@ant-design/icons-vue';\r\n\r\nexport default {\r\n  name: 'UserView',\r\n  data() {\r\n    return {\r\n      currentComponent: 'UserInfo',\r\n    };\r\n  },\r\n  methods: {\r\n    changeComponent(componentName) {\r\n      this.currentComponent = componentName;\r\n    },\r\n    goToLogin() {\r\n      this.currentComponent = 'UserInfo';\r\n      this.$router.push('/');\r\n    },\r\n    goToMain() {\r\n      this.currentComponent = 'UserInfo';\r\n      this.$router.push('/main');\r\n    },\r\n    getAvatarText() {\r\n      // 获取用户名的首字母或前两个字符作为头像文本\r\n      const username = this.$store.state.username;\r\n      if (!username) return '?';\r\n\r\n      // 如果用户名是中文，取第一个字符\r\n      if (/[\\u4e00-\\u9fa5]/.test(username)) {\r\n        return username.substring(0, 1);\r\n      }\r\n\r\n      // 如果是英文，取首字母大写\r\n      return username.substring(0, 1).toUpperCase();\r\n    },\r\n  },\r\n  components: {\r\n    UserInfo,\r\n    UserChangePassword,\r\n    UserChangeEmail,\r\n    IdcardOutlined,\r\n    TeamOutlined,\r\n    UserAdmin,\r\n    LoginOutlined,\r\n    HomeFilled,\r\n    MailOutlined,\r\n    InsuranceOutlined\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.user-view-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 247, 255, 0.85) 100%);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  margin: 20px;\r\n  position: relative;\r\n}\r\n\r\n.user-view-container::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-image: url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%231890ff' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E\");\r\n  opacity: 0.5;\r\n  z-index: 0;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 侧边栏样式 */\r\n.sidebar {\r\n  width: 280px;\r\n  background-color: #1890ff;\r\n  color: white;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 30px 0;\r\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.sidebar::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: url('../assets/decorative_pattern.svg') no-repeat center center;\r\n  background-size: 200%;\r\n  opacity: 0.05;\r\n  z-index: 0;\r\n}\r\n\r\n.user-profile {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n  margin-bottom: 15px;\r\n  display: inline-block;\r\n}\r\n\r\n.user-avatar {\r\n  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);\r\n  border: 3px solid rgba(255, 255, 255, 0.3);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-avatar:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);\r\n  border: 3px solid rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.avatar-text {\r\n  font-size: 36px;\r\n  font-weight: bold;\r\n  color: white;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.avatar-status {\r\n  position: absolute;\r\n  bottom: 5px;\r\n  right: 5px;\r\n  width: 15px;\r\n  height: 15px;\r\n  background-color: #52c41a;\r\n  border-radius: 50%;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);\r\n  }\r\n}\r\n\r\n.username {\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n  margin: 10px 0 5px;\r\n  color: white;\r\n}\r\n\r\n.user-role {\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  margin: 0;\r\n}\r\n\r\n.menu-items {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 10px 15px;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  margin-bottom: 10px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  background: transparent;\r\n  border: none;\r\n  text-align: left;\r\n  font-size: 16px;\r\n}\r\n\r\n.menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  transform: translateX(5px);\r\n}\r\n\r\n.menu-item.selected {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  font-weight: 500;\r\n}\r\n\r\n.menu-item .anticon {\r\n  margin-right: 12px;\r\n  font-size: 18px;\r\n}\r\n\r\n.bottom-actions {\r\n  padding: 20px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.action-button {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 10px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n  font-size: 16px;\r\n}\r\n\r\n.action-button .anticon {\r\n  margin-right: 8px;\r\n}\r\n\r\n.home-button {\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.home-button:hover {\r\n  background-color: #40a9ff;\r\n  border-color: #40a9ff;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.logout-button {\r\n  background-color: #f56565;\r\n  border-color: #f56565;\r\n}\r\n\r\n.logout-button:hover {\r\n  background-color: #e53e3e;\r\n  border-color: #e53e3e;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 30px;\r\n  overflow-y: auto;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(245, 250, 255, 0.9) 100%);\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.content-area::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-image: url(\"data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%234299e1' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E\");\r\n  z-index: -1;\r\n  opacity: 0.7;\r\n  pointer-events: none;\r\n}\r\n\r\n.content-header {\r\n  margin-bottom: 30px;\r\n  border-bottom: 1px solid rgba(226, 232, 240, 0.6);\r\n  padding-bottom: 15px;\r\n  position: relative;\r\n}\r\n\r\n.content-header::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: -1px;\r\n  left: 0;\r\n  width: 100px;\r\n  height: 3px;\r\n  background-color: #1890ff;\r\n  border-radius: 3px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #1890ff;\r\n  margin: 0;\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.content-body {\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  border-radius: 12px;\r\n  padding: 25px;\r\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);\r\n  min-height: 500px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(226, 232, 240, 0.8);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.content-body::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 5px;\r\n  background-color: #1890ff;\r\n  opacity: 0.7;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .user-view-container {\r\n    flex-direction: column;\r\n    margin: 0;\r\n    height: auto;\r\n    min-height: 100vh;\r\n  }\r\n\r\n  .sidebar {\r\n    width: 100%;\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .content-area {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AAyFA,OAAOA,QAAO,MAAO,6BAA6B;AAClD,OAAOC,kBAAiB,MAAO,+BAA+B;AAC9D,OAAOC,eAAc,MAAO,gCAAgC;AAC5D,OAAOC,SAAQ,MAAO,8BAA8B;AACpD,SAASC,aAAa,EAAEC,UAAU,EAAEC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,QAAO,uBAAuB;AAE/H,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACPC,eAAeA,CAACC,aAAa,EAAE;MAC7B,IAAI,CAACH,gBAAe,GAAIG,aAAa;IACvC,CAAC;IACDC,SAASA,CAAA,EAAG;MACV,IAAI,CAACJ,gBAAe,GAAI,UAAU;MAClC,IAAI,CAACK,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;IACxB,CAAC;IACDC,QAAQA,CAAA,EAAG;MACT,IAAI,CAACP,gBAAe,GAAI,UAAU;MAClC,IAAI,CAACK,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;IAC5B,CAAC;IACDE,aAAaA,CAAA,EAAG;MACd;MACA,MAAMC,QAAO,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAQ;MAC3C,IAAI,CAACA,QAAQ,EAAE,OAAO,GAAG;;MAEzB;MACA,IAAI,iBAAiB,CAACG,IAAI,CAACH,QAAQ,CAAC,EAAE;QACpC,OAAOA,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;MACjC;;MAEA;MACA,OAAOJ,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC/C;EACF,CAAC;EACDC,UAAU,EAAE;IACV3B,QAAQ;IACRC,kBAAkB;IAClBC,eAAe;IACfM,cAAc;IACdF,YAAY;IACZH,SAAS;IACTC,aAAa;IACbC,UAAU;IACVE,YAAY;IACZE;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}