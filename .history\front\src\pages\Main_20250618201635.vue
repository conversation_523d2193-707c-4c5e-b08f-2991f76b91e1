<template>
  <div class="main-container">
    <div class="main-header">
      <div class="user-info fade-in">
        <div class="avatar-container">
          <a-avatar :size="40" class="user-avatar">
            <template #icon>
              <span class="avatar-text">{{ getAvatarText() }}</span>
            </template>
          </a-avatar>
          <div class="avatar-status"></div>
        </div>
        <span class="username">{{ $store.state.username }}</span>
      </div>
      <a-button @click="goToLogin" type="text" class="logout-button fade-in">
        <LogoutOutlined />
        退出登录
      </a-button>
    </div>

    <div class="main-content">
      <div class="logo-container slide-in-up">
        <img src="../assets/logo.svg" alt="明瞳智治Logo" class="main-logo" />
      </div>
      <h1 class="welcome-title slide-in-up delay-1">明瞳智治：城市多模态智能治理与视频定位</h1>
      <p class="welcome-subtitle slide-in-up delay-2">多模态检测技术助力城市精细化管理</p>



      <div class="feature-cards">
        <div class="feature-card slide-in-up delay-3 hover-lift" @click="goToUserManagement">
          <div class="card-icon">
            <ContactsFilled />
          </div>
          <h3>个人中心</h3>
          <p>管理您的账户信息和权限设置</p>
        </div>

        <div class="feature-card slide-in-up delay-4 hover-lift" @click="goToTaskManagement">
          <div class="card-icon">
            <ProfileFilled />
          </div>
          <h3>监测任务</h3>
          <p>创建和管理城市监测分析任务</p>
        </div>
      </div>
    </div>

    <div class="main-footer fade-in delay-5">
      <div class="footer-content">
        <p>明瞳智治：城市多模态智能治理与视频定位 - 多模态检测技术助力城市精细化管理</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ContactsFilled, ProfileFilled, LogoutOutlined } from '@ant-design/icons-vue';

export default {
  name: 'MainView',
  components: {
    ContactsFilled,
    ProfileFilled,
    LogoutOutlined
  },
  methods: {
    goToUserManagement() {
      this.$router.push('/user');
    },
    goToTaskManagement() {
      this.$router.push('/task');
    },
    goToLogin() {
      this.$message.success('已安全退出系统');
      this.$router.push('/');
    },
    getAvatarText() {
      // 获取用户名的首字母或前两个字符作为头像文本
      const username = this.$store.state.username;
      if (!username) return '?';

      // 如果用户名是中文，取第一个字符
      if (/[\u4e00-\u9fa5]/.test(username)) {
        return username.substring(0, 1);
      }

      // 如果是英文，取首字母大写
      return username.substring(0, 1).toUpperCase();
    }
  },
};
</script>

<style scoped>
.main-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.user-avatar {
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.avatar-text {
  font-size: 18px;
  font-weight: bold;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.avatar-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background-color: #52c41a;
  border-radius: 50%;
  border: 1.5px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

.username {
  font-weight: 500;
  font-size: 16px;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ff4d4f;
  transition: all 0.3s;
}

.logout-button:hover {
  color: #ff7875;
  transform: translateY(-2px);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.logo-container {
  margin-bottom: 30px;
}

.main-logo {
  width: 120px;
  height: 120px;
  object-fit: contain;
  filter: drop-shadow(0 4px 16px rgba(24, 144, 255, 0.3));
  transition: all 0.4s ease;
  animation: logoFloat 3s ease-in-out infinite;
}

.main-logo:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 6px 20px rgba(24, 144, 255, 0.4));
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.welcome-title {
  font-size: 48px;
  font-weight: 800;
  margin-bottom: 15px;
  color: #1890ff;
  text-shadow: 0 2px 10px rgba(24, 144, 255, 0.2);
  letter-spacing: -0.5px;
}

.welcome-subtitle {
  font-size: 20px;
  color: #666;
  margin-bottom: 60px;
  max-width: 600px;
  line-height: 1.6;
}

.theme-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 40px;
}

.stat-item {
  text-align: center;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  min-width: 120px;
  border: 1px solid rgba(24, 144, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%231890ff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
  z-index: -1;
  opacity: 0.5;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.feature-cards {
  display: flex;
  gap: 40px;
  justify-content: center;
  flex-wrap: wrap;
}

.feature-card {
  width: 320px;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: #1890ff;
  opacity: 0.8;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 40px;
  color: #1890ff;
  margin-bottom: 25px;
  background-color: rgba(24, 144, 255, 0.1);
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
  transition: all 0.3s ease;
}

.feature-card:hover .card-icon {
  transform: scale(1.1);
  background-color: rgba(24, 144, 255, 0.15);
}

.feature-card h3 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 15px;
  color: #333;
  position: relative;
  display: inline-block;
}

.feature-card h3::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: #1890ff;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.feature-card:hover h3::after {
  width: 60px;
  opacity: 0.8;
}

.feature-card p {
  color: #666;
  font-size: 16px;
  line-height: 1.6;
}

.main-footer {
  padding: 20px;
  color: #666;
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.5);
  position: relative;
  z-index: 10;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-content p {
  text-align: center;
  font-size: 16px;
  color: #666;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-links a {
  color: #666;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .main-header {
    padding: 15px 20px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .theme-stats {
    flex-direction: column;
    gap: 15px;
  }

  .stat-item {
    width: 100%;
  }

  .feature-cards {
    flex-direction: column;
    gap: 20px;
  }

  .feature-card {
    width: 100%;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>
