{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nimport _imports_0 from '../assets/logo.svg';\nconst _withScopeId = n => (_pushScopeId(\"data-v-1f24bdc7\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"register-container\"\n};\nconst _hoisted_2 = {\n  class: \"register-card fade-in\"\n};\nconst _hoisted_3 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"register-header\"\n}, [/*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"logo-container\"\n}, [/*#__PURE__*/_createElementVNode(\"img\", {\n  src: _imports_0,\n  alt: \"Logo\",\n  class: \"register-logo\"\n})]), /*#__PURE__*/_createElementVNode(\"h1\", {\n  class: \"register-title\"\n}, \"用户注册\")], -1 /* HOISTED */));\nconst _hoisted_4 = {\n  class: \"register-form\"\n};\nconst _hoisted_5 = {\n  class: \"verification-code-container slide-in-up delay-5\"\n};\nconst _hoisted_6 = {\n  class: \"register-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ArrowLeftOutlined = _resolveComponent(\"ArrowLeftOutlined\");\n  const _component_a_button = _resolveComponent(\"a-button\");\n  const _component_a_input = _resolveComponent(\"a-input\");\n  const _component_a_input_password = _resolveComponent(\"a-input-password\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_a_button, {\n    onClick: $options.goToLogin,\n    type: \"default\",\n    class: \"back-to-login-btn\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_ArrowLeftOutlined, {\n      style: {\n        fontSize: '18px'\n      }\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _hoisted_3, _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_a_input, {\n    value: $data.username,\n    \"onUpdate:value\": _cache[0] || (_cache[0] = $event => $data.username = $event),\n    placeholder: \"用户名\",\n    size: \"large\",\n    class: \"register-input slide-in-up delay-1\"\n  }, null, 8 /* PROPS */, [\"value\"]), _createVNode(_component_a_input_password, {\n    value: $data.password,\n    \"onUpdate:value\": _cache[1] || (_cache[1] = $event => $data.password = $event),\n    class: _normalizeClass([{\n      'error-input': $options.passwordMismatch\n    }, \"register-input slide-in-up delay-2\"]),\n    placeholder: \"密码\",\n    size: \"large\"\n  }, null, 8 /* PROPS */, [\"value\", \"class\"]), _createVNode(_component_a_input_password, {\n    value: $data.confirmPassword,\n    \"onUpdate:value\": _cache[2] || (_cache[2] = $event => $data.confirmPassword = $event),\n    class: _normalizeClass([{\n      'error-input': $options.passwordMismatch\n    }, \"register-input slide-in-up delay-3\"]),\n    placeholder: \"确认密码\",\n    size: \"large\"\n  }, null, 8 /* PROPS */, [\"value\", \"class\"]), _createVNode(_component_a_input, {\n    value: $data.email,\n    \"onUpdate:value\": _cache[3] || (_cache[3] = $event => $data.email = $event),\n    placeholder: \"邮箱\",\n    size: \"large\",\n    class: \"register-input slide-in-up delay-4\"\n  }, null, 8 /* PROPS */, [\"value\"]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_a_input, {\n    value: $data.verificationCode,\n    \"onUpdate:value\": _cache[4] || (_cache[4] = $event => $data.verificationCode = $event),\n    placeholder: \"验证码\",\n    size: \"large\",\n    class: \"verification-input\"\n  }, null, 8 /* PROPS */, [\"value\"]), _createVNode(_component_a_button, {\n    onClick: $options.sendVerificationCode,\n    type: \"default\",\n    size: \"large\",\n    class: \"verification-btn\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(\" 发送验证码 \")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_a_button, {\n    onClick: $options.register,\n    type: \"primary\",\n    size: \"large\",\n    class: \"register-button slide-in-up delay-6\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(\" 注册 \")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])])])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementVNode", "src", "alt", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_a_button", "onClick", "$options", "goToLogin", "type", "default", "_withCtx", "_component_ArrowLeftOutlined", "style", "fontSize", "_", "_hoisted_3", "_hoisted_4", "_component_a_input", "value", "$data", "username", "_cache", "$event", "placeholder", "size", "_component_a_input_password", "password", "_normalizeClass", "passwordMismatch", "confirmPassword", "email", "_hoisted_5", "verificationCode", "sendVerificationCode", "_createTextVNode", "_hoisted_6", "register"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\pages\\Register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"register-container\">\r\n    <div class=\"register-card fade-in\">\r\n      <a-button @click=\"goToLogin\" type=\"default\" class=\"back-to-login-btn\">\r\n        <ArrowLeftOutlined :style=\"{ fontSize: '18px' }\"/>\r\n      </a-button>\r\n      <div class=\"register-header\">\r\n        <div class=\"logo-container\">\r\n          <img src=\"../assets/logo.svg\" alt=\"Logo\" class=\"register-logo\" />\r\n        </div>\r\n        <h1 class=\"register-title\">用户注册</h1>\r\n      </div>\r\n      <div class=\"register-form\">\r\n        <a-input\r\n          v-model:value=\"username\"\r\n          placeholder=\"用户名\"\r\n          size=\"large\"\r\n          class=\"register-input slide-in-up delay-1\">\r\n        </a-input>\r\n\r\n        <a-input-password\r\n          v-model:value=\"password\"\r\n          :class=\"{ 'error-input': passwordMismatch }\"\r\n          placeholder=\"密码\"\r\n          size=\"large\"\r\n          class=\"register-input slide-in-up delay-2\">\r\n        </a-input-password>\r\n\r\n        <a-input-password\r\n          v-model:value=\"confirmPassword\"\r\n          :class=\"{ 'error-input': passwordMismatch }\"\r\n          placeholder=\"确认密码\"\r\n          size=\"large\"\r\n          class=\"register-input slide-in-up delay-3\">\r\n        </a-input-password>\r\n\r\n        <a-input\r\n          v-model:value=\"email\"\r\n          placeholder=\"邮箱\"\r\n          size=\"large\"\r\n          class=\"register-input slide-in-up delay-4\">\r\n        </a-input>\r\n\r\n        <div class=\"verification-code-container slide-in-up delay-5\">\r\n          <a-input\r\n            v-model:value=\"verificationCode\"\r\n            placeholder=\"验证码\"\r\n            size=\"large\"\r\n            class=\"verification-input\">\r\n          </a-input>\r\n          <a-button\r\n            @click=\"sendVerificationCode\"\r\n            type=\"default\"\r\n            size=\"large\"\r\n            class=\"verification-btn\">\r\n            发送验证码\r\n          </a-button>\r\n        </div>\r\n\r\n        <div class=\"register-actions\">\r\n          <a-button\r\n            @click=\"register\"\r\n            type=\"primary\"\r\n            size=\"large\"\r\n            class=\"register-button slide-in-up delay-6\">\r\n            注册\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n  <script>\r\n  import { ArrowLeftOutlined } from '@ant-design/icons-vue';\r\n  export default {\r\n    name: 'RegisterView',\r\n    components: {\r\n        ArrowLeftOutlined\r\n    },\r\n    data() {\r\n      return {\r\n        username: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        email: '',\r\n        verificationCode: '',\r\n        trueCode: ''\r\n      };\r\n    },\r\n    // 刚切换到注册页面时，清空输入框\r\n    beforeRouteLeave(to, from, next) {\r\n      this.username = '';\r\n      this.password = '';\r\n      this.confirmPassword = '';\r\n      this.email = '';\r\n      this.verificationCode = '';\r\n      this.trueCode = '';\r\n      next();\r\n    },\r\n    computed: {\r\n      passwordMismatch() {\r\n        return this.password !== this.confirmPassword;\r\n      }\r\n    },\r\n    methods: {\r\n      sendVerificationCode() {\r\n        // 实现发送验证码的逻辑\r\n        console.log('发送验证码到邮箱:', this.email);\r\n        this.$axios.post('/sendVerificationCode', {\r\n          email: this.email\r\n        }).then((res) => {\r\n          if (res.data.code === 200) {\r\n            this.trueCode=res.data.verificationCode;\r\n            this.$message.success('验证码发送成功');\r\n          } else {\r\n            console.error('验证码发送失败:', res.data.message);\r\n          }\r\n        }).catch((err) => {\r\n          console.error('验证码发送失败:', err);\r\n        });\r\n      },\r\n      register() {\r\n        // 实现注册逻辑\r\n        console.log('用户名:', this.username);\r\n        console.log('密码:', this.password);\r\n        console.log('确认密码:', this.confirmPassword);\r\n        console.log('邮箱:', this.email);\r\n        console.log('邮箱验证码:', this.verificationCode);\r\n\r\n        if (this.passwordMismatch) {\r\n          // 处理密码不匹配的情况，可以显示提示信息或者采取其他操作\r\n          this.$message.error('两次输入的密码不一致');\r\n          // 清空确认密码输入框\r\n          this.confirmPassword = '';\r\n          return;\r\n        }\r\n        if(this.verificationCode!==this.trueCode){\r\n          this.$message.error('验证码错误');\r\n          // 清空验证码输入框\r\n          this.verificationCode = '';\r\n          return;\r\n        }\r\n        // 发送注册请求\r\n        this.$axios.post('/register', {\r\n          username: this.username,\r\n          password: this.password,\r\n          email: this.email\r\n        }).then((res) => {\r\n          if (res.data.code === 200) {\r\n            this.$message.success('注册成功');\r\n            // 注册成功后，可以将用户重定向到登录页面\r\n            this.$router.push('/');\r\n          } else {\r\n            this.$message.error('注册失败:' + res.data.message);\r\n          }\r\n        }).catch((err) => {\r\n          console.error('注册失败:', err);\r\n          this.$message.error('注册失败，未知错误!');\r\n        });\r\n\r\n      },\r\n      goToLogin() {\r\n        // 点击返回登录按钮时的逻辑，重定向到登录页面\r\n        this.$router.push('/');\r\n      }\r\n    }\r\n  };\r\n  </script>\r\n\r\n  <style scoped>\r\n.register-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 100vh;\r\n  background: url('../assets/bg.png') no-repeat center center/cover;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.register-card {\r\n  width: 450px;\r\n  padding: 40px;\r\n  background: rgba(255, 255, 255, 0.85);\r\n  border-radius: 16px;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.5);\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.register-card:hover {\r\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.register-card::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 5px;\r\n  height: 100%;\r\n  background: #1890ff;\r\n  opacity: 0.8;\r\n}\r\n\r\n.register-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.register-title {\r\n  font-size: 32px;\r\n  color: #1890ff;\r\n  margin-bottom: 20px;\r\n  font-weight: 600;\r\n}\r\n\r\n.register-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.register-input {\r\n  margin-bottom: 20px;\r\n  height: 50px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.verification-code-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.verification-input {\r\n  flex: 1;\r\n  height: 50px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.verification-btn {\r\n  height: 50px;\r\n  border-radius: 8px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.register-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.register-button {\r\n  height: 50px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  transition: all 0.3s;\r\n  background-color: #1890ff;\r\n  border: none;\r\n}\r\n\r\n.register-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 7px 14px rgba(24, 144, 255, 0.2), 0 3px 6px rgba(0, 0, 0, 0.08);\r\n  background-color: #40a9ff;\r\n}\r\n\r\n.back-to-login-btn {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  border: none;\r\n  background: transparent;\r\n  box-shadow: none;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.back-to-login-btn:hover {\r\n  color: #1890ff;\r\n  transform: translateX(-3px);\r\n}\r\n\r\n.error-input {\r\n  border-color: #ff4d4f !important;\r\n}\r\n\r\n/* 动画效果 */\r\n.fade-in {\r\n  animation: fadeIn 0.8s ease-in-out;\r\n}\r\n\r\n.slide-in-up {\r\n  animation: slideInUp 0.5s ease-in-out;\r\n}\r\n\r\n.delay-1 {\r\n  animation-delay: 0.1s;\r\n}\r\n\r\n.delay-2 {\r\n  animation-delay: 0.2s;\r\n}\r\n\r\n.delay-3 {\r\n  animation-delay: 0.3s;\r\n}\r\n\r\n.delay-4 {\r\n  animation-delay: 0.4s;\r\n}\r\n\r\n.delay-5 {\r\n  animation-delay: 0.5s;\r\n}\r\n\r\n.delay-6 {\r\n  animation-delay: 0.6s;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    transform: translateY(20px);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 600px) {\r\n  .register-card {\r\n    width: 90%;\r\n    padding: 30px 20px;\r\n  }\r\n\r\n  .register-title {\r\n    font-size: 28px;\r\n  }\r\n}\r\n  </style>\r\n"], "mappings": ";OAQeA,UAAwB;;;EAPhCC,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAuB;gEAIhCC,mBAAA,CAKM;EALDD,KAAK,EAAC;AAAiB,I,aAC1BC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAgB,I,aACzBC,mBAAA,CAAiE;EAA5DC,GAAwB,EAAxBH,UAAwB;EAACI,GAAG,EAAC,MAAM;EAACH,KAAK,EAAC;mBAEjDC,mBAAA,CAAoC;EAAhCD,KAAK,EAAC;AAAgB,GAAC,MAAI,E;;EAE5BA,KAAK,EAAC;AAAe;;EA+BnBA,KAAK,EAAC;AAAiD;;EAgBvDA,KAAK,EAAC;AAAkB;;;;;;uBA1DnCI,mBAAA,CAqEM,OArENC,UAqEM,GApEJJ,mBAAA,CAmEM,OAnENK,UAmEM,GAlEJC,YAAA,CAEWC,mBAAA;IAFAC,OAAK,EAAEC,QAAA,CAAAC,SAAS;IAAEC,IAAI,EAAC,SAAS;IAACZ,KAAK,EAAC;;IAHxDa,OAAA,EAAAC,QAAA,CAIQ,MAAkD,CAAlDP,YAAA,CAAkDQ,4BAAA;MAA9BC,KAAK,EAAE;QAAAC,QAAA;MAAA;IAAoB,G;IAJvDC,CAAA;kCAMMC,UAKM,EACNlB,mBAAA,CAwDM,OAxDNmB,UAwDM,GAvDJb,YAAA,CAKUc,kBAAA;IAJAC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAdjC,kBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAcyBH,KAAA,CAAAC,QAAQ,GAAAE,MAAA;IACvBC,WAAW,EAAC,KAAK;IACjBC,IAAI,EAAC,OAAO;IACZ5B,KAAK,EAAC;sCAGRO,YAAA,CAMmBsB,2BAAA;IALTP,KAAK,EAAEC,KAAA,CAAAO,QAAQ;IArBjC,kBAAAL,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqByBH,KAAA,CAAAO,QAAQ,GAAAJ,MAAA;IACtB1B,KAAK,EAtBhB+B,eAAA;MAAA,eAsBmCrB,QAAA,CAAAsB;IAAgB,GAGnC,oCAAoC;IAF1CL,WAAW,EAAC,IAAI;IAChBC,IAAI,EAAC;+CAIPrB,YAAA,CAMmBsB,2BAAA;IALTP,KAAK,EAAEC,KAAA,CAAAU,eAAe;IA7BxC,kBAAAR,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA6ByBH,KAAA,CAAAU,eAAe,GAAAP,MAAA;IAC7B1B,KAAK,EA9BhB+B,eAAA;MAAA,eA8BmCrB,QAAA,CAAAsB;IAAgB,GAGnC,oCAAoC;IAF1CL,WAAW,EAAC,MAAM;IAClBC,IAAI,EAAC;+CAIPrB,YAAA,CAKUc,kBAAA;IAJAC,KAAK,EAAEC,KAAA,CAAAW,KAAK;IArC9B,kBAAAT,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqCyBH,KAAA,CAAAW,KAAK,GAAAR,MAAA;IACpBC,WAAW,EAAC,IAAI;IAChBC,IAAI,EAAC,OAAO;IACZ5B,KAAK,EAAC;sCAGRC,mBAAA,CAcM,OAdNkC,UAcM,GAbJ5B,YAAA,CAKUc,kBAAA;IAJAC,KAAK,EAAEC,KAAA,CAAAa,gBAAgB;IA7C3C,kBAAAX,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA6C2BH,KAAA,CAAAa,gBAAgB,GAAAV,MAAA;IAC/BC,WAAW,EAAC,KAAK;IACjBC,IAAI,EAAC,OAAO;IACZ5B,KAAK,EAAC;sCAERO,YAAA,CAMWC,mBAAA;IALRC,OAAK,EAAEC,QAAA,CAAA2B,oBAAoB;IAC5BzB,IAAI,EAAC,SAAS;IACdgB,IAAI,EAAC,OAAO;IACZ5B,KAAK,EAAC;;IAtDlBa,OAAA,EAAAC,QAAA,CAsDqC,MAE3B,CAxDVwB,gBAAA,CAsDqC,SAE3B,E;IAxDVpB,CAAA;oCA2DQjB,mBAAA,CAQM,OARNsC,UAQM,GAPJhC,YAAA,CAMWC,mBAAA;IALRC,OAAK,EAAEC,QAAA,CAAA8B,QAAQ;IAChB5B,IAAI,EAAC,SAAS;IACdgB,IAAI,EAAC,OAAO;IACZ5B,KAAK,EAAC;;IAhElBa,OAAA,EAAAC,QAAA,CAgEwD,MAE9C,CAlEVwB,gBAAA,CAgEwD,MAE9C,E;IAlEVpB,CAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}