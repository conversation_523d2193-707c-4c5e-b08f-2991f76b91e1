{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { PlusOutlined, HomeFilled, LogoutOutlined, ReloadOutlined, SearchOutlined, EyeOutlined, DeleteOutlined, FileOutlined, LoadingOutlined, CheckCircleOutlined, ClockCircleOutlined, TableOutlined, AppstoreOutlined, SyncOutlined } from '@ant-design/icons-vue';\nexport default {\n  name: 'TaskView',\n  components: {\n    PlusOutlined,\n    HomeFilled,\n    LogoutOutlined,\n    ReloadOutlined,\n    SearchOutlined,\n    EyeOutlined,\n    DeleteOutlined,\n    FileOutlined,\n    LoadingOutlined,\n    CheckCircleOutlined,\n    ClockCircleOutlined,\n    TableOutlined,\n    AppstoreOutlined,\n    SyncOutlined\n  },\n  data() {\n    return {\n      columns: [\n      //   { title: 'ID', dataIndex: 'id', key: 'id' },\n      {\n        title: '创建时间',\n        dataIndex: 'create_date',\n        key: 'create_date',\n        sorter: (a, b) => a.create_date.localeCompare(b.create_date)\n      }, {\n        title: '更新时间',\n        dataIndex: 'update_date',\n        key: 'update_date',\n        sorter: (a, b) => a.update_date.localeCompare(b.update_date)\n      }, {\n        title: '模式',\n        dataIndex: 'task_mode',\n        key: 'task_mode',\n        sorter: (a, b) => a.task_mode.localeCompare(b.task_mode)\n      }, {\n        title: '状态',\n        dataIndex: 'task_status',\n        key: 'task_status',\n        sorter: (a, b) => a.task_status.localeCompare(b.task_status)\n      }, {\n        title: '监测任务描述',\n        dataIndex: 'task_description',\n        key: 'task_description'\n      }, {\n        title: '',\n        dataIndex: 'check',\n        key: 'check'\n      }, {\n        title: '',\n        dataIndex: 'operation',\n        key: 'operation'\n      }],\n      dataSource: [],\n      openCreateTask: false,\n      selectMode: 'balanced',\n      description: '',\n      search: '',\n      viewMode: 'table' // 默认表格视图\n    };\n  },\n  // 刚进入该路由时获取会话数据\n  beforeRouteEnter(to, from, next) {\n    next(vm => {\n      vm.getTasks();\n    });\n  },\n  methods: {\n    // 获取特定状态的会话数量\n    getStatusCount(status) {\n      return this.dataSource.filter(task => task.task_status === status).length;\n    },\n    getTasks() {\n      this.$message.info('正在获取会话数据...'); // Show a message to indicate the data is being fetched\n      // Assuming this.$axios is configured correctly for your backend API\n      this.$axios.post('/getTaskByUser', {\n        username: this.$store.state.username\n      }).then(res => {\n        if (res.data.code === 200) {\n          console.log('获取会话数据成功:', res.data.tasks);\n          this.dataSource = res.data.tasks; // Adjust based on your actual API response structure\n          console.log('获取会话数据成功:', this.dataSource);\n        } else {\n          console.error('获取会话数据失败:', res.data.message);\n        }\n      }).catch(err => {\n        console.error('获取会话数据失败:', err);\n      });\n    },\n    addTask() {\n      this.openCreateTask = true;\n    },\n    handleOk() {\n      this.$axios.post('/createTask', {\n        username: this.$store.state.username,\n        task_mode: this.selectMode,\n        task_description: this.description\n      }).then(res => {\n        if (res.data.code === 200) {\n          this.$message.success('创建会话成功', res.data.task_id);\n          this.getTasks();\n        } else {\n          this.$message.error('创建会话失败:' + res.data.message);\n        }\n      }).catch(err => {\n        this.$message.error('创建会话失败:' + err);\n      });\n      this.selectMode = 'balanced';\n      this.description = '';\n      this.openCreateTask = false;\n    },\n    returnMain() {\n      this.$router.push('/main');\n    },\n    logOut() {\n      this.$router.push('/');\n    },\n    checkThisTask(record) {\n      this.$message.info('查看会话详情:' + record.id);\n      this.$axios.post('updateTaskTime', {\n        task_id: record.id\n      }).then(res => {\n        if (res.data.code === 200) {\n          console.log('更新会话时间成功:', res.data.task);\n          if (record.task_mode === 'balanced') {\n            this.$store.dispatch('updateTaskRecord', res.data.task);\n            this.$router.push('/task/balanced');\n          }\n        } else {\n          console.error('更新会话时间失败:', res.data.message);\n        }\n      }).catch(err => {\n        console.error('更新会话时间失败:', err);\n      });\n    },\n    showDeleteConfirm(record) {\n      // 显示加载提示\n      const hideLoading = this.$message.loading('正在删除会话...', 0);\n\n      // 直接发送删除请求，不显示确认对话框\n      this.$axios.post('/deleteTask', {\n        task_id: record.id,\n        username: this.$store.state.username\n      }, {\n        timeout: 30000,\n        // 增加超时时间到30秒\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      }).then(res => {\n        // 关闭加载提示\n        hideLoading();\n        if (res.data.code === 200) {\n          this.$message.success('删除会话成功');\n          this.getTasks();\n        } else if (res.data.code === 206) {\n          // 部分成功，任务记录已删除但文件可能未完全删除\n          this.$message.warning(res.data.message || '任务记录已删除，但部分文件可能未被删除');\n          this.getTasks();\n        } else {\n          this.$message.error('删除会话失败: ' + (res.data.message || '未知错误'));\n        }\n      }).catch(err => {\n        // 关闭加载提示\n        hideLoading();\n        console.error('删除会话失败:', err);\n        // 显示更友好的错误信息\n        this.$message.error('删除会话失败，请稍后重试');\n      });\n    },\n    searchTask() {\n      this.$message.info('正在搜索会话...');\n      if (this.search === '') {\n        this.getTasks();\n      } else {\n        this.$axios.post('/getTaskByUser', {\n          username: this.$store.state.username\n        }).then(res => {\n          if (res.data.code === 200) {\n            let searchResult = res.data.tasks.filter(task => {\n              return task.task_description.includes(this.search);\n            });\n            this.dataSource = searchResult; // Adjust based on your actual API response structure\n            console.log('获取会话数据成功:', this.dataSource);\n          } else {\n            console.error('获取会话数据失败:', res.data.message);\n          }\n        }).catch(err => {\n          console.error('获取会话数据失败:', err);\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["PlusOutlined", "HomeFilled", "LogoutOutlined", "ReloadOutlined", "SearchOutlined", "EyeOutlined", "DeleteOutlined", "FileOutlined", "LoadingOutlined", "CheckCircleOutlined", "ClockCircleOutlined", "TableOutlined", "AppstoreOutlined", "SyncOutlined", "name", "components", "data", "columns", "title", "dataIndex", "key", "sorter", "a", "b", "create_date", "localeCompare", "update_date", "task_mode", "task_status", "dataSource", "openCreateTask", "selectMode", "description", "search", "viewMode", "beforeRouteEnter", "to", "from", "next", "vm", "getTasks", "methods", "getStatusCount", "status", "filter", "task", "length", "$message", "info", "$axios", "post", "username", "$store", "state", "then", "res", "code", "console", "log", "tasks", "error", "message", "catch", "err", "addTask", "handleOk", "task_description", "success", "task_id", "return<PERSON>ain", "$router", "push", "logOut", "checkThisTask", "record", "id", "dispatch", "showDeleteConfirm", "hideLoading", "loading", "timeout", "headers", "warning", "searchTask", "searchResult", "includes"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\pages\\Task.vue"], "sourcesContent": ["<template>\r\n  <div class=\"task-container\">\r\n    <div class=\"task-header\">\r\n      <div class=\"header-left fade-in\">\r\n        <a-button @click=\"returnMain\" type=\"text\" class=\"nav-button\">\r\n          <HomeFilled />\r\n          返回首页\r\n        </a-button>\r\n        <div class=\"title-with-logo\">\r\n          <img src=\"../assets/logo.svg\" alt=\"Logo\" class=\"task-logo\" />\r\n          <h1 class=\"page-title\">城市监测任务</h1>\r\n        </div>\r\n      </div>\r\n      <div class=\"header-right fade-in\">\r\n        <div class=\"search-box\">\r\n          <a-input\r\n            placeholder=\"搜索监测任务...\"\r\n            v-model:value=\"search\"\r\n            class=\"search-input\"\r\n          >\r\n            <template #prefix><SearchOutlined /></template>\r\n          </a-input>\r\n          <a-button type=\"primary\" @click=\"searchTask\" class=\"search-button\">\r\n            搜索\r\n          </a-button>\r\n        </div>\r\n        <a-button type=\"primary\" @click=\"getTasks\" class=\"refresh-button\">\r\n          <ReloadOutlined />\r\n          刷新\r\n        </a-button>\r\n        <a-button type=\"primary\" @click=\"addTask\" class=\"add-button\">\r\n          <PlusOutlined />\r\n          新建监测\r\n        </a-button>\r\n        <a-button @click=\"logOut\" type=\"text\" class=\"logout-button\">\r\n          <LogoutOutlined />\r\n        </a-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"task-content slide-in-up\">\r\n      <!-- 统计卡片 -->\r\n      <div class=\"stats-cards\">\r\n        <div class=\"stat-card fade-in\">\r\n          <div class=\"stat-icon total-icon\">\r\n            <FileOutlined />\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <h3>总监测数</h3>\r\n            <p class=\"stat-value\">{{ dataSource.length }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"stat-card fade-in delay-1\">\r\n          <div class=\"stat-icon running-icon\">\r\n            <LoadingOutlined />\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <h3>处理中</h3>\r\n            <p class=\"stat-value\">{{ getStatusCount('running') }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"stat-card fade-in delay-2\">\r\n          <div class=\"stat-icon finished-icon\">\r\n            <CheckCircleOutlined />\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <h3>已完成</h3>\r\n            <p class=\"stat-value\">{{ getStatusCount('finished') }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"stat-card fade-in delay-3\">\r\n          <div class=\"stat-icon preparing-icon\">\r\n            <ClockCircleOutlined />\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <h3>待处理</h3>\r\n            <p class=\"stat-value\">{{ getStatusCount('preparing') }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div class=\"table-container\">\r\n        <div class=\"table-header\">\r\n          <h2 class=\"section-title\">监测任务列表</h2>\r\n          <div class=\"view-toggle\">\r\n            <a-button-group>\r\n              <a-button type=\"primary\" :ghost=\"viewMode !== 'table'\" @click=\"viewMode = 'table'\">\r\n                <TableOutlined />\r\n                表格视图\r\n              </a-button>\r\n              <a-button type=\"primary\" :ghost=\"viewMode !== 'card'\" @click=\"viewMode = 'card'\">\r\n                <AppstoreOutlined />\r\n                卡片视图\r\n              </a-button>\r\n            </a-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <a-table\r\n          v-if=\"viewMode === 'table'\"\r\n          :dataSource=\"dataSource\"\r\n          :columns=\"columns\"\r\n          :pagination=\"{\r\n            showSizeChanger: false,\r\n            defaultPageSize: 8,\r\n            showTotal: total => `共 ${total} 条记录`\r\n          }\"\r\n          class=\"task-table\"\r\n          :rowClassName=\"() => 'task-row'\"\r\n        >\r\n          <template v-slot:bodyCell=\"{ column, record }\">\r\n            <template v-if=\"column.dataIndex === 'task_mode'\">\r\n              <a-tag v-if=\"record.task_mode === 'balanced'\" color=\"blue\" class=\"mode-tag\">综合监测</a-tag>\r\n              <a-tag v-else-if=\"record.task_mode === 'efficient'\" color=\"green\" class=\"mode-tag\">快速监测</a-tag>\r\n              <a-tag v-else-if=\"record.task_mode === 'accurate'\" color=\"red\" class=\"mode-tag\">精确监测</a-tag>\r\n            </template>\r\n            <template v-else-if=\"column.dataIndex === 'task_status'\">\r\n              <a-tag v-if=\"record.task_status === 'preparing'\" color=\"blue\" class=\"status-tag\">待处理</a-tag>\r\n              <a-tag v-else-if=\"record.task_status === 'running'\" color=\"orange\" class=\"status-tag\">处理中</a-tag>\r\n              <a-tag v-else-if=\"record.task_status === 'finished'\" color=\"green\" class=\"status-tag\">已完成</a-tag>\r\n            </template>\r\n            <template v-else-if=\"column.dataIndex === 'check'\">\r\n              <a-button @click=\"checkThisTask(record)\" type=\"primary\" class=\"action-button view-button\">\r\n                <EyeOutlined />\r\n                查看\r\n              </a-button>\r\n            </template>\r\n            <template v-else-if=\"column.dataIndex === 'operation'\">\r\n              <a-button @click=\"showDeleteConfirm(record)\" type=\"primary\" danger class=\"action-button delete-button\">\r\n                <DeleteOutlined />\r\n                删除\r\n              </a-button>\r\n            </template>\r\n          </template>\r\n        </a-table>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-else class=\"task-cards\">\r\n          <div v-for=\"record in dataSource\" :key=\"record.id\" class=\"task-card hover-lift\">\r\n            <div class=\"task-card-header\">\r\n              <div class=\"task-card-title\">\r\n                <h3>{{ record.task_description || '无描述' }}</h3>\r\n                <a-tag v-if=\"record.task_mode === 'balanced'\" color=\"blue\" class=\"mode-tag\">综合监测</a-tag>\r\n                <a-tag v-else-if=\"record.task_mode === 'efficient'\" color=\"green\" class=\"mode-tag\">快速监测</a-tag>\r\n                <a-tag v-else-if=\"record.task_mode === 'accurate'\" color=\"red\" class=\"mode-tag\">精确监测</a-tag>\r\n              </div>\r\n              <div class=\"task-card-status\">\r\n                <a-tag v-if=\"record.task_status === 'preparing'\" color=\"blue\" class=\"status-tag\">待处理</a-tag>\r\n                <a-tag v-else-if=\"record.task_status === 'running'\" color=\"orange\" class=\"status-tag\">处理中</a-tag>\r\n                <a-tag v-else-if=\"record.task_status === 'finished'\" color=\"green\" class=\"status-tag\">已完成</a-tag>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"task-card-body\">\r\n              <div class=\"task-card-info\">\r\n                <p><ClockCircleOutlined /> 创建时间: {{ record.create_date }}</p>\r\n                <p><SyncOutlined /> 更新时间: {{ record.update_date }}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"task-card-footer\">\r\n              <a-button @click=\"checkThisTask(record)\" type=\"primary\" class=\"action-button view-button\">\r\n                <EyeOutlined />\r\n                查看\r\n              </a-button>\r\n              <a-button @click=\"showDeleteConfirm(record)\" type=\"primary\" danger class=\"action-button delete-button\">\r\n                <DeleteOutlined />\r\n                删除\r\n              </a-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <a-modal\r\n      v-model:open=\"openCreateTask\"\r\n      title=\"创建新监测任务\"\r\n      @ok=\"handleOk\"\r\n      class=\"create-task-modal\"\r\n      cancelText=\"取消\"\r\n      okText=\"创建\"\r\n      :maskClosable=\"false\"\r\n    >\r\n      <div class=\"modal-content\">\r\n        <div class=\"form-item\">\r\n          <label>选择模式:</label>\r\n          <a-select\r\n            v-model:value=\"selectMode\"\r\n            class=\"mode-select\"\r\n          >\r\n            <a-select-option value=\"balanced\">综合监测</a-select-option>\r\n            <a-select-option value=\"efficient\">快速监测</a-select-option>\r\n            <a-select-option value=\"accurate\">精确监测</a-select-option>\r\n          </a-select>\r\n        </div>\r\n        <div class=\"form-item\">\r\n          <label>监测任务描述:</label>\r\n          <a-textarea\r\n            v-model:value=\"description\"\r\n            placeholder=\"请输入监测任务描述...\"\r\n            :auto-size=\"{ minRows: 3, maxRows: 6 }\"\r\n            class=\"description-textarea\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n  <script>\r\n  import {\r\n    PlusOutlined,\r\n    HomeFilled,\r\n    LogoutOutlined,\r\n    ReloadOutlined,\r\n    SearchOutlined,\r\n    EyeOutlined,\r\n    DeleteOutlined,\r\n    FileOutlined,\r\n    LoadingOutlined,\r\n    CheckCircleOutlined,\r\n    ClockCircleOutlined,\r\n    TableOutlined,\r\n    AppstoreOutlined,\r\n    SyncOutlined\r\n  } from '@ant-design/icons-vue';\r\n\r\n  export default {\r\n    name: 'TaskView',\r\n    components: {\r\n        PlusOutlined,\r\n        HomeFilled,\r\n        LogoutOutlined,\r\n        ReloadOutlined,\r\n        SearchOutlined,\r\n        EyeOutlined,\r\n        DeleteOutlined,\r\n        FileOutlined,\r\n        LoadingOutlined,\r\n        CheckCircleOutlined,\r\n        ClockCircleOutlined,\r\n        TableOutlined,\r\n        AppstoreOutlined,\r\n        SyncOutlined\r\n    },\r\n    data() {\r\n      return {\r\n        columns: [\r\n        //   { title: 'ID', dataIndex: 'id', key: 'id' },\r\n          { title: '创建时间', dataIndex: 'create_date', key: 'create_date', sorter: (a, b) => a.create_date.localeCompare(b.create_date) },\r\n          { title: '更新时间', dataIndex: 'update_date', key: 'update_date', sorter: (a, b) => a.update_date.localeCompare(b.update_date) },\r\n          { title: '模式', dataIndex: 'task_mode', key: 'task_mode' ,sorter: (a, b) => a.task_mode.localeCompare(b.task_mode)},\r\n          { title: '状态', dataIndex: 'task_status', key: 'task_status'  ,sorter: (a, b) => a.task_status.localeCompare(b.task_status)},\r\n          { title: '监测任务描述', dataIndex: 'task_description', key: 'task_description' },\r\n          { title: '', dataIndex: 'check', key: 'check' },\r\n          { title: '', dataIndex: 'operation', key: 'operation' }\r\n        ],\r\n        dataSource: [],\r\n        openCreateTask: false,\r\n        selectMode: 'balanced',\r\n        description: '',\r\n        search: '',\r\n        viewMode: 'table', // 默认表格视图\r\n      };\r\n    },\r\n    // 刚进入该路由时获取会话数据\r\n    beforeRouteEnter(to, from, next) {\r\n      next((vm) => {\r\n        vm.getTasks();\r\n      });\r\n    },\r\n    methods: {\r\n      // 获取特定状态的会话数量\r\n      getStatusCount(status) {\r\n        return this.dataSource.filter(task => task.task_status === status).length;\r\n      },\r\n\r\n      getTasks() {\r\n        this.$message.info('正在获取会话数据...'); // Show a message to indicate the data is being fetched\r\n        // Assuming this.$axios is configured correctly for your backend API\r\n        this.$axios.post('/getTaskByUser', { username: this.$store.state.username }).then((res) => {\r\n          if (res.data.code === 200) {\r\n            console.log('获取会话数据成功:', res.data.tasks);\r\n            this.dataSource = res.data.tasks; // Adjust based on your actual API response structure\r\n            console.log('获取会话数据成功:', this.dataSource);\r\n          } else {\r\n            console.error('获取会话数据失败:', res.data.message);\r\n          }\r\n        }).catch((err) => {\r\n          console.error('获取会话数据失败:', err);\r\n        });\r\n      },\r\n        addTask() {\r\n            this.openCreateTask = true;\r\n\r\n        },\r\n        handleOk() {\r\n            this.$axios.post('/createTask', { username: this.$store.state.username, task_mode: this.selectMode, task_description: this.description }).then((res) => {\r\n                if (res.data.code === 200) {\r\n                    this.$message.success('创建会话成功',res.data.task_id);\r\n                    this.getTasks();\r\n                } else {\r\n                    this.$message.error('创建会话失败:' + res.data.message);\r\n                }\r\n            }).catch((err) => {\r\n                this.$message.error('创建会话失败:' + err);\r\n            });\r\n            this.selectMode = 'balanced';\r\n            this.description = '';\r\n            this.openCreateTask = false;\r\n        },\r\n        returnMain() {\r\n            this.$router.push('/main');\r\n        },\r\n        logOut() {\r\n            this.$router.push('/');\r\n        },\r\n        checkThisTask(record) {\r\n            this.$message.info('查看会话详情:' + record.id);\r\n            this.$axios.post('updateTaskTime', { task_id: record.id }).then((res) => {\r\n                if (res.data.code === 200) {\r\n                    console.log('更新会话时间成功:', res.data.task);\r\n                    if(record.task_mode === 'balanced'){\r\n                        this.$store.dispatch('updateTaskRecord', res.data.task);\r\n                        this.$router.push('/task/balanced');\r\n                    }\r\n                } else {\r\n                    console.error('更新会话时间失败:', res.data.message);\r\n                }\r\n            }).catch((err) => {\r\n                console.error('更新会话时间失败:', err);\r\n            });\r\n        },\r\n        showDeleteConfirm(record) {\r\n            // 显示加载提示\r\n            const hideLoading = this.$message.loading('正在删除会话...', 0);\r\n\r\n            // 直接发送删除请求，不显示确认对话框\r\n            this.$axios.post('/deleteTask', {\r\n                task_id: record.id,\r\n                username: this.$store.state.username\r\n            }, {\r\n                timeout: 30000, // 增加超时时间到30秒\r\n                headers: {\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            }).then((res) => {\r\n                // 关闭加载提示\r\n                hideLoading();\r\n\r\n                if (res.data.code === 200) {\r\n                    this.$message.success('删除会话成功');\r\n                    this.getTasks();\r\n                } else if (res.data.code === 206) {\r\n                    // 部分成功，任务记录已删除但文件可能未完全删除\r\n                    this.$message.warning(res.data.message || '任务记录已删除，但部分文件可能未被删除');\r\n                    this.getTasks();\r\n                } else {\r\n                    this.$message.error('删除会话失败: ' + (res.data.message || '未知错误'));\r\n                }\r\n            }).catch((err) => {\r\n                // 关闭加载提示\r\n                hideLoading();\r\n\r\n                console.error('删除会话失败:', err);\r\n                // 显示更友好的错误信息\r\n                this.$message.error('删除会话失败，请稍后重试');\r\n            });\r\n        },\r\n        searchTask() {\r\n            this.$message.info('正在搜索会话...');\r\n            if (this.search === '') {\r\n                this.getTasks();\r\n            } else {\r\n                this.$axios.post('/getTaskByUser', { username: this.$store.state.username }).then((res) => {\r\n                if (res.data.code === 200) {\r\n                    let searchResult = res.data.tasks.filter((task) => {\r\n                        return task.task_description.includes(this.search);\r\n                    });\r\n                    this.dataSource = searchResult; // Adjust based on your actual API response structure\r\n                    console.log('获取会话数据成功:', this.dataSource);\r\n                } else {\r\n                    console.error('获取会话数据失败:', res.data.message);\r\n                }\r\n                }).catch((err) => {\r\n                console.error('获取会话数据失败:', err);\r\n                });\r\n\r\n\r\n            }\r\n        },\r\n    },\r\n  };\r\n  </script>\r\n  <style scoped>\r\n.task-container {\r\n  min-height: 100vh;\r\n  padding: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  background: rgba(255, 255, 255, 0.85);\r\n  padding: 25px;\r\n  border-radius: 16px;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.6);\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 5px;\r\n  height: 100%;\r\n  background-color: #1890ff;\r\n  opacity: 0.8;\r\n  z-index: 2;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 16px;\r\n  color: #1890ff;\r\n}\r\n\r\n.page-title {\r\n  font-size: 28px;\r\n  font-weight: 800;\r\n  margin: 0;\r\n  color: #1890ff;\r\n  text-shadow: 0 1px 2px rgba(24, 144, 255, 0.1);\r\n  letter-spacing: -0.5px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 15px;\r\n  align-items: center;\r\n}\r\n\r\n.search-box {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.search-input {\r\n  width: 250px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.search-button, .refresh-button, .add-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.search-button:hover, .refresh-button:hover, .add-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.logout-button {\r\n  color: #ff4d4f;\r\n  font-size: 18px;\r\n}\r\n\r\n.task-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n  flex: 1;\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-cards {\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.stat-card {\r\n  flex: 1;\r\n  min-width: 200px;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  border-radius: 12px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  transition: all 0.3s;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.6);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background-color: #1890ff;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stat-card:hover::after {\r\n  opacity: 1;\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  margin-right: 15px;\r\n}\r\n\r\n.total-icon {\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n}\r\n\r\n.running-icon {\r\n  background-color: #fff7e6;\r\n  color: #fa8c16;\r\n}\r\n\r\n.finished-icon {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n}\r\n\r\n.preparing-icon {\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n}\r\n\r\n.stat-info {\r\n  flex: 1;\r\n}\r\n\r\n.stat-info h3 {\r\n  font-size: 16px;\r\n  color: #666;\r\n  margin: 0 0 5px 0;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  margin: 0;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  background: rgba(255, 255, 255, 0.85);\r\n  border-radius: 16px;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\r\n  padding: 30px;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.6);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.table-container::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 100%;\r\n  background-image: url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%231890ff' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E\");\r\n  z-index: 0;\r\n  opacity: 0.5;\r\n  pointer-events: none;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 22px;\r\n  font-weight: 700;\r\n  color: #1890ff;\r\n  margin: 0;\r\n  position: relative;\r\n  display: inline-block;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.section-title::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 40px;\r\n  height: 3px;\r\n  background-color: #1890ff;\r\n  opacity: 0.7;\r\n}\r\n\r\n.task-table {\r\n  width: 100%;\r\n}\r\n\r\n.task-row {\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.task-row:hover {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n/* 卡片视图样式 */\r\n.task-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.task-card {\r\n  background: rgba(255, 255, 255, 0.8);\r\n  border-radius: 12px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  transition: all 0.3s;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(255, 255, 255, 0.6);\r\n  position: relative;\r\n}\r\n\r\n.task-card::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: -2px;\r\n  left: -2px;\r\n  right: -2px;\r\n  bottom: -2px;\r\n  background-color: #1890ff;\r\n  z-index: -1;\r\n  border-radius: 14px;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n}\r\n\r\n.task-card:hover::before {\r\n  opacity: 0.5;\r\n}\r\n\r\n.task-card-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.task-card-title {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.task-card-title h3 {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin: 0 0 10px 0;\r\n  color: #333;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.task-card-body {\r\n  padding: 15px;\r\n}\r\n\r\n.task-card-info p {\r\n  margin: 5px 0;\r\n  color: #666;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.task-card-info .anticon {\r\n  margin-right: 8px;\r\n}\r\n\r\n.task-card-footer {\r\n  padding: 15px;\r\n  border-top: 1px solid #f0f0f0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.mode-tag, .status-tag {\r\n  font-size: 14px;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.action-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.view-button {\r\n  background-color: #1890ff;\r\n}\r\n\r\n.delete-button {\r\n  background-color: #ff4d4f;\r\n}\r\n\r\n.action-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.create-task-modal {\r\n  max-width: 500px;\r\n}\r\n\r\n.modal-content {\r\n  padding: 10px;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-item label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.mode-select {\r\n  width: 100%;\r\n  border-radius: 4px;\r\n}\r\n\r\n.description-textarea {\r\n  width: 100%;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .task-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .header-right {\r\n    width: 100%;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .search-box {\r\n    width: 100%;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .search-input {\r\n    flex: 1;\r\n  }\r\n}\r\n</style>"], "mappings": ";AAwNE,SACEA,YAAY,EACZC,UAAU,EACVC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,aAAa,EACbC,gBAAgB,EAChBC,YAAW,QACN,uBAAuB;AAE9B,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IACRf,YAAY;IACZC,UAAU;IACVC,cAAc;IACdC,cAAc;IACdC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,YAAY;IACZC,eAAe;IACfC,mBAAmB;IACnBC,mBAAmB;IACnBC,aAAa;IACbC,gBAAgB;IAChBC;EACJ,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;MACT;MACE;QAAEC,KAAK,EAAE,MAAM;QAAEC,SAAS,EAAE,aAAa;QAAEC,GAAG,EAAE,aAAa;QAAEC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,WAAW,CAACC,aAAa,CAACF,CAAC,CAACC,WAAW;MAAE,CAAC,EAC7H;QAAEN,KAAK,EAAE,MAAM;QAAEC,SAAS,EAAE,aAAa;QAAEC,GAAG,EAAE,aAAa;QAAEC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACI,WAAW,CAACD,aAAa,CAACF,CAAC,CAACG,WAAW;MAAE,CAAC,EAC7H;QAAER,KAAK,EAAE,IAAI;QAAEC,SAAS,EAAE,WAAW;QAAEC,GAAG,EAAE,WAAU;QAAGC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACK,SAAS,CAACF,aAAa,CAACF,CAAC,CAACI,SAAS;MAAC,CAAC,EAClH;QAAET,KAAK,EAAE,IAAI;QAAEC,SAAS,EAAE,aAAa;QAAEC,GAAG,EAAE,aAAY;QAAIC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACM,WAAW,CAACH,aAAa,CAACF,CAAC,CAACK,WAAW;MAAC,CAAC,EAC3H;QAAEV,KAAK,EAAE,QAAQ;QAAEC,SAAS,EAAE,kBAAkB;QAAEC,GAAG,EAAE;MAAmB,CAAC,EAC3E;QAAEF,KAAK,EAAE,EAAE;QAAEC,SAAS,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAC,EAC/C;QAAEF,KAAK,EAAE,EAAE;QAAEC,SAAS,EAAE,WAAW;QAAEC,GAAG,EAAE;MAAY,EACvD;MACDS,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,UAAU;MACtBC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,OAAO,CAAE;IACrB,CAAC;EACH,CAAC;EACD;EACAC,gBAAgBA,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE;IAC/BA,IAAI,CAAEC,EAAE,IAAK;MACXA,EAAE,CAACC,QAAQ,CAAC,CAAC;IACf,CAAC,CAAC;EACJ,CAAC;EACDC,OAAO,EAAE;IACP;IACAC,cAAcA,CAACC,MAAM,EAAE;MACrB,OAAO,IAAI,CAACd,UAAU,CAACe,MAAM,CAACC,IAAG,IAAKA,IAAI,CAACjB,WAAU,KAAMe,MAAM,CAAC,CAACG,MAAM;IAC3E,CAAC;IAEDN,QAAQA,CAAA,EAAG;MACT,IAAI,CAACO,QAAQ,CAACC,IAAI,CAAC,aAAa,CAAC,EAAE;MACnC;MACA,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF;MAAS,CAAC,CAAC,CAACG,IAAI,CAAEC,GAAG,IAAK;QACzF,IAAIA,GAAG,CAACvC,IAAI,CAACwC,IAAG,KAAM,GAAG,EAAE;UACzBC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEH,GAAG,CAACvC,IAAI,CAAC2C,KAAK,CAAC;UACxC,IAAI,CAAC9B,UAAS,GAAI0B,GAAG,CAACvC,IAAI,CAAC2C,KAAK,EAAE;UAClCF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC7B,UAAU,CAAC;QAC3C,OAAO;UACL4B,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEL,GAAG,CAACvC,IAAI,CAAC6C,OAAO,CAAC;QAC9C;MACF,CAAC,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAK;QAChBN,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEG,GAAG,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;IACCC,OAAOA,CAAA,EAAG;MACN,IAAI,CAAClC,cAAa,GAAI,IAAI;IAE9B,CAAC;IACDmC,QAAQA,CAAA,EAAG;MACP,IAAI,CAAChB,MAAM,CAACC,IAAI,CAAC,aAAa,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAQ;QAAExB,SAAS,EAAE,IAAI,CAACI,UAAU;QAAEmC,gBAAgB,EAAE,IAAI,CAAClC;MAAY,CAAC,CAAC,CAACsB,IAAI,CAAEC,GAAG,IAAK;QACpJ,IAAIA,GAAG,CAACvC,IAAI,CAACwC,IAAG,KAAM,GAAG,EAAE;UACvB,IAAI,CAACT,QAAQ,CAACoB,OAAO,CAAC,QAAQ,EAACZ,GAAG,CAACvC,IAAI,CAACoD,OAAO,CAAC;UAChD,IAAI,CAAC5B,QAAQ,CAAC,CAAC;QACnB,OAAO;UACH,IAAI,CAACO,QAAQ,CAACa,KAAK,CAAC,SAAQ,GAAIL,GAAG,CAACvC,IAAI,CAAC6C,OAAO,CAAC;QACrD;MACJ,CAAC,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAK;QACd,IAAI,CAAChB,QAAQ,CAACa,KAAK,CAAC,SAAQ,GAAIG,GAAG,CAAC;MACxC,CAAC,CAAC;MACF,IAAI,CAAChC,UAAS,GAAI,UAAU;MAC5B,IAAI,CAACC,WAAU,GAAI,EAAE;MACrB,IAAI,CAACF,cAAa,GAAI,KAAK;IAC/B,CAAC;IACDuC,UAAUA,CAAA,EAAG;MACT,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;IAC9B,CAAC;IACDC,MAAMA,CAAA,EAAG;MACL,IAAI,CAACF,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;IAC1B,CAAC;IACDE,aAAaA,CAACC,MAAM,EAAE;MAClB,IAAI,CAAC3B,QAAQ,CAACC,IAAI,CAAC,SAAQ,GAAI0B,MAAM,CAACC,EAAE,CAAC;MACzC,IAAI,CAAC1B,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAE;QAAEkB,OAAO,EAAEM,MAAM,CAACC;MAAG,CAAC,CAAC,CAACrB,IAAI,CAAEC,GAAG,IAAK;QACrE,IAAIA,GAAG,CAACvC,IAAI,CAACwC,IAAG,KAAM,GAAG,EAAE;UACvBC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEH,GAAG,CAACvC,IAAI,CAAC6B,IAAI,CAAC;UACvC,IAAG6B,MAAM,CAAC/C,SAAQ,KAAM,UAAU,EAAC;YAC/B,IAAI,CAACyB,MAAM,CAACwB,QAAQ,CAAC,kBAAkB,EAAErB,GAAG,CAACvC,IAAI,CAAC6B,IAAI,CAAC;YACvD,IAAI,CAACyB,OAAO,CAACC,IAAI,CAAC,gBAAgB,CAAC;UACvC;QACJ,OAAO;UACHd,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEL,GAAG,CAACvC,IAAI,CAAC6C,OAAO,CAAC;QAChD;MACJ,CAAC,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAK;QACdN,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEG,GAAG,CAAC;MACnC,CAAC,CAAC;IACN,CAAC;IACDc,iBAAiBA,CAACH,MAAM,EAAE;MACtB;MACA,MAAMI,WAAU,GAAI,IAAI,CAAC/B,QAAQ,CAACgC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;;MAEzD;MACA,IAAI,CAAC9B,MAAM,CAACC,IAAI,CAAC,aAAa,EAAE;QAC5BkB,OAAO,EAAEM,MAAM,CAACC,EAAE;QAClBxB,QAAQ,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF;MAChC,CAAC,EAAE;QACC6B,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;UACL,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC,CAAC3B,IAAI,CAAEC,GAAG,IAAK;QACb;QACAuB,WAAW,CAAC,CAAC;QAEb,IAAIvB,GAAG,CAACvC,IAAI,CAACwC,IAAG,KAAM,GAAG,EAAE;UACvB,IAAI,CAACT,QAAQ,CAACoB,OAAO,CAAC,QAAQ,CAAC;UAC/B,IAAI,CAAC3B,QAAQ,CAAC,CAAC;QACnB,OAAO,IAAIe,GAAG,CAACvC,IAAI,CAACwC,IAAG,KAAM,GAAG,EAAE;UAC9B;UACA,IAAI,CAACT,QAAQ,CAACmC,OAAO,CAAC3B,GAAG,CAACvC,IAAI,CAAC6C,OAAM,IAAK,qBAAqB,CAAC;UAChE,IAAI,CAACrB,QAAQ,CAAC,CAAC;QACnB,OAAO;UACH,IAAI,CAACO,QAAQ,CAACa,KAAK,CAAC,UAAS,IAAKL,GAAG,CAACvC,IAAI,CAAC6C,OAAM,IAAK,MAAM,CAAC,CAAC;QAClE;MACJ,CAAC,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAK;QACd;QACAe,WAAW,CAAC,CAAC;QAEbrB,OAAO,CAACG,KAAK,CAAC,SAAS,EAAEG,GAAG,CAAC;QAC7B;QACA,IAAI,CAAChB,QAAQ,CAACa,KAAK,CAAC,cAAc,CAAC;MACvC,CAAC,CAAC;IACN,CAAC;IACDuB,UAAUA,CAAA,EAAG;MACT,IAAI,CAACpC,QAAQ,CAACC,IAAI,CAAC,WAAW,CAAC;MAC/B,IAAI,IAAI,CAACf,MAAK,KAAM,EAAE,EAAE;QACpB,IAAI,CAACO,QAAQ,CAAC,CAAC;MACnB,OAAO;QACH,IAAI,CAACS,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAE;UAAEC,QAAQ,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF;QAAS,CAAC,CAAC,CAACG,IAAI,CAAEC,GAAG,IAAK;UAC3F,IAAIA,GAAG,CAACvC,IAAI,CAACwC,IAAG,KAAM,GAAG,EAAE;YACvB,IAAI4B,YAAW,GAAI7B,GAAG,CAACvC,IAAI,CAAC2C,KAAK,CAACf,MAAM,CAAEC,IAAI,IAAK;cAC/C,OAAOA,IAAI,CAACqB,gBAAgB,CAACmB,QAAQ,CAAC,IAAI,CAACpD,MAAM,CAAC;YACtD,CAAC,CAAC;YACF,IAAI,CAACJ,UAAS,GAAIuD,YAAY,EAAE;YAChC3B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC7B,UAAU,CAAC;UAC7C,OAAO;YACH4B,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEL,GAAG,CAACvC,IAAI,CAAC6C,OAAO,CAAC;UAChD;QACA,CAAC,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAK;UAClBN,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEG,GAAG,CAAC;QAC/B,CAAC,CAAC;MAGN;IACJ;EACJ;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}