{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ContactsFilled, ProfileFilled, LogoutOutlined } from '@ant-design/icons-vue';\nexport default {\n  name: 'MainView',\n  components: {\n    ContactsFilled,\n    ProfileFilled,\n    LogoutOutlined\n  },\n  methods: {\n    goToUserManagement() {\n      this.$router.push('/user');\n    },\n    goToTaskManagement() {\n      this.$router.push('/task');\n    },\n    goToLogin() {\n      this.$message.success('已安全退出系统');\n      this.$router.push('/');\n    },\n    getAvatarText() {\n      // 获取用户名的首字母或前两个字符作为头像文本\n      const username = this.$store.state.username;\n      if (!username) return '?';\n\n      // 如果用户名是中文，取第一个字符\n      if (/[\\u4e00-\\u9fa5]/.test(username)) {\n        return username.substring(0, 1);\n      }\n\n      // 如果是英文，取首字母大写\n      return username.substring(0, 1).toUpperCase();\n    }\n  }\n};", "map": {"version": 3, "names": ["ContactsFilled", "ProfileFilled", "LogoutOutlined", "name", "components", "methods", "goToUserManagement", "$router", "push", "goToTaskManagement", "goToLogin", "$message", "success", "getAvatarText", "username", "$store", "state", "test", "substring", "toUpperCase"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\pages\\Main.vue"], "sourcesContent": ["<template>\r\n  <div class=\"main-container\">\r\n    <div class=\"main-header\">\r\n      <div class=\"header-left\">\r\n        <div class=\"main-logo-container\">\r\n          <img src=\"../assets/logo.svg\" alt=\"Logo\" class=\"main-logo\" />\r\n        </div>\r\n        <div class=\"user-info fade-in\">\r\n          <div class=\"avatar-container\">\r\n            <a-avatar :size=\"40\" class=\"user-avatar\">\r\n              <template #icon>\r\n                <span class=\"avatar-text\">{{ getAvatarText() }}</span>\r\n              </template>\r\n            </a-avatar>\r\n            <div class=\"avatar-status\"></div>\r\n          </div>\r\n          <span class=\"username\">{{ $store.state.username }}</span>\r\n        </div>\r\n      </div>\r\n      <a-button @click=\"goToLogin\" type=\"text\" class=\"logout-button fade-in\">\r\n        <LogoutOutlined />\r\n        退出登录\r\n      </a-button>\r\n    </div>\r\n\r\n    <div class=\"main-content\">\r\n      <h1 class=\"welcome-title slide-in-up\">明瞳智治：城市多模态智能治理与视频定位</h1>\r\n      <p class=\"welcome-subtitle slide-in-up delay-1\">多模态检测技术助力城市精细化管理</p>\r\n\r\n\r\n\r\n      <div class=\"feature-cards\">\r\n        <div class=\"feature-card slide-in-up delay-3 hover-lift\" @click=\"goToUserManagement\">\r\n          <div class=\"card-icon\">\r\n            <ContactsFilled />\r\n          </div>\r\n          <h3>个人中心</h3>\r\n          <p>管理您的账户信息和权限设置</p>\r\n        </div>\r\n\r\n        <div class=\"feature-card slide-in-up delay-4 hover-lift\" @click=\"goToTaskManagement\">\r\n          <div class=\"card-icon\">\r\n            <ProfileFilled />\r\n          </div>\r\n          <h3>监测任务</h3>\r\n          <p>创建和管理城市监测分析任务</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"main-footer fade-in delay-5\">\r\n      <div class=\"footer-content\">\r\n        <p>明瞳智治：城市多模态智能治理与视频定位 - 多模态检测技术助力城市精细化管理</p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ContactsFilled, ProfileFilled, LogoutOutlined } from '@ant-design/icons-vue';\r\n\r\nexport default {\r\n  name: 'MainView',\r\n  components: {\r\n    ContactsFilled,\r\n    ProfileFilled,\r\n    LogoutOutlined\r\n  },\r\n  methods: {\r\n    goToUserManagement() {\r\n      this.$router.push('/user');\r\n    },\r\n    goToTaskManagement() {\r\n      this.$router.push('/task');\r\n    },\r\n    goToLogin() {\r\n      this.$message.success('已安全退出系统');\r\n      this.$router.push('/');\r\n    },\r\n    getAvatarText() {\r\n      // 获取用户名的首字母或前两个字符作为头像文本\r\n      const username = this.$store.state.username;\r\n      if (!username) return '?';\r\n\r\n      // 如果用户名是中文，取第一个字符\r\n      if (/[\\u4e00-\\u9fa5]/.test(username)) {\r\n        return username.substring(0, 1);\r\n      }\r\n\r\n      // 如果是英文，取首字母大写\r\n      return username.substring(0, 1).toUpperCase();\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.main-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.main-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 40px;\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.main-logo-container {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.main-logo {\r\n  width: 50px;\r\n  height: 50px;\r\n  object-fit: contain;\r\n  filter: drop-shadow(0 2px 8px rgba(24, 144, 255, 0.2));\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.main-logo:hover {\r\n  transform: scale(1.1);\r\n  filter: drop-shadow(0 4px 12px rgba(24, 144, 255, 0.3));\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.user-avatar {\r\n  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-avatar:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n  border: 2px solid rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.avatar-text {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: white;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.avatar-status {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  width: 10px;\r\n  height: 10px;\r\n  background-color: #52c41a;\r\n  border-radius: 50%;\r\n  border: 1.5px solid white;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(82, 196, 26, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);\r\n  }\r\n}\r\n\r\n.username {\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n}\r\n\r\n.logout-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #ff4d4f;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.logout-button:hover {\r\n  color: #ff7875;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.main-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80px 20px;\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 48px;\r\n  font-weight: 800;\r\n  margin-bottom: 15px;\r\n  color: #1890ff;\r\n  text-shadow: 0 2px 10px rgba(24, 144, 255, 0.2);\r\n  letter-spacing: -0.5px;\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: 20px;\r\n  color: #666;\r\n  margin-bottom: 60px;\r\n  max-width: 600px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.theme-stats {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 40px;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n  padding: 15px 25px;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  border-radius: 12px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n  min-width: 120px;\r\n  border: 1px solid rgba(24, 144, 255, 0.2);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-item::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-image: url(\"data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%231890ff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E\");\r\n  z-index: -1;\r\n  opacity: 0.5;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #1890ff;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.feature-cards {\r\n  display: flex;\r\n  gap: 40px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.feature-card {\r\n  width: 320px;\r\n  padding: 40px;\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  border-radius: 24px;\r\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n  cursor: pointer;\r\n  border: 1px solid rgba(255, 255, 255, 0.8);\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.feature-card::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 5px;\r\n  height: 100%;\r\n  background: #1890ff;\r\n  opacity: 0.8;\r\n}\r\n\r\n.feature-card:hover {\r\n  transform: translateY(-10px);\r\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-icon {\r\n  font-size: 40px;\r\n  color: #1890ff;\r\n  margin-bottom: 25px;\r\n  background-color: rgba(24, 144, 255, 0.1);\r\n  width: 100px;\r\n  height: 100px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.feature-card:hover .card-icon {\r\n  transform: scale(1.1);\r\n  background-color: rgba(24, 144, 255, 0.15);\r\n}\r\n\r\n.feature-card h3 {\r\n  font-size: 22px;\r\n  font-weight: 700;\r\n  margin-bottom: 15px;\r\n  color: #333;\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.feature-card h3::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: -8px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40px;\r\n  height: 3px;\r\n  background-color: #1890ff;\r\n  opacity: 0.5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.feature-card:hover h3::after {\r\n  width: 60px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.feature-card p {\r\n  color: #666;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.main-footer {\r\n  padding: 20px;\r\n  color: #666;\r\n  background-color: rgba(255, 255, 255, 0.85);\r\n  backdrop-filter: blur(10px);\r\n  border-top: 1px solid rgba(255, 255, 255, 0.5);\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n.footer-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n}\r\n\r\n.footer-content p {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  color: #666;\r\n}\r\n\r\n.footer-links {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.footer-links a {\r\n  color: #666;\r\n  text-decoration: none;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.footer-links a:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 600px) {\r\n  .main-header {\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .theme-stats {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n  }\r\n\r\n  .stat-item {\r\n    width: 100%;\r\n  }\r\n\r\n  .feature-cards {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n\r\n  .feature-card {\r\n    width: 100%;\r\n  }\r\n\r\n  .footer-content {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .footer-links {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AA2DA,SAASA,cAAc,EAAEC,aAAa,EAAEC,cAAa,QAAS,uBAAuB;AAErF,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IACVJ,cAAc;IACdC,aAAa;IACbC;EACF,CAAC;EACDG,OAAO,EAAE;IACPC,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;IAC5B,CAAC;IACDC,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACF,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;IAC5B,CAAC;IACDE,SAASA,CAAA,EAAG;MACV,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,SAAS,CAAC;MAChC,IAAI,CAACL,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;IACxB,CAAC;IACDK,aAAaA,CAAA,EAAG;MACd;MACA,MAAMC,QAAO,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAQ;MAC3C,IAAI,CAACA,QAAQ,EAAE,OAAO,GAAG;;MAEzB;MACA,IAAI,iBAAiB,CAACG,IAAI,CAACH,QAAQ,CAAC,EAAE;QACpC,OAAOA,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;MACjC;;MAEA;MACA,OAAOJ,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC/C;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}