{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ArrowLeftOutlined } from '@ant-design/icons-vue';\nexport default {\n  name: 'RegisterView',\n  components: {\n    ArrowLeftOutlined\n  },\n  data() {\n    return {\n      username: '',\n      password: '',\n      confirmPassword: '',\n      email: '',\n      verificationCode: '',\n      trueCode: ''\n    };\n  },\n  // 刚切换到注册页面时，清空输入框\n  beforeRouteLeave(to, from, next) {\n    this.username = '';\n    this.password = '';\n    this.confirmPassword = '';\n    this.email = '';\n    this.verificationCode = '';\n    this.trueCode = '';\n    next();\n  },\n  computed: {\n    passwordMismatch() {\n      return this.password !== this.confirmPassword;\n    }\n  },\n  methods: {\n    sendVerificationCode() {\n      // 实现发送验证码的逻辑\n      console.log('发送验证码到邮箱:', this.email);\n      this.$axios.post('/sendVerificationCode', {\n        email: this.email\n      }).then(res => {\n        if (res.data.code === 200) {\n          this.trueCode = res.data.verificationCode;\n          this.$message.success('验证码发送成功');\n        } else {\n          console.error('验证码发送失败:', res.data.message);\n        }\n      }).catch(err => {\n        console.error('验证码发送失败:', err);\n      });\n    },\n    register() {\n      // 实现注册逻辑\n      console.log('用户名:', this.username);\n      console.log('密码:', this.password);\n      console.log('确认密码:', this.confirmPassword);\n      console.log('邮箱:', this.email);\n      console.log('邮箱验证码:', this.verificationCode);\n      if (this.passwordMismatch) {\n        // 处理密码不匹配的情况，可以显示提示信息或者采取其他操作\n        this.$message.error('两次输入的密码不一致');\n        // 清空确认密码输入框\n        this.confirmPassword = '';\n        return;\n      }\n      if (this.verificationCode !== this.trueCode) {\n        this.$message.error('验证码错误');\n        // 清空验证码输入框\n        this.verificationCode = '';\n        return;\n      }\n      // 发送注册请求\n      this.$axios.post('/register', {\n        username: this.username,\n        password: this.password,\n        email: this.email\n      }).then(res => {\n        if (res.data.code === 200) {\n          this.$message.success('注册成功');\n          // 注册成功后，可以将用户重定向到登录页面\n          this.$router.push('/');\n        } else {\n          this.$message.error('注册失败:' + res.data.message);\n        }\n      }).catch(err => {\n        console.error('注册失败:', err);\n        this.$message.error('注册失败，未知错误!');\n      });\n    },\n    goToLogin() {\n      // 点击返回登录按钮时的逻辑，重定向到登录页面\n      this.$router.push('/');\n    }\n  }\n};", "map": {"version": 3, "names": ["ArrowLeftOutlined", "name", "components", "data", "username", "password", "confirmPassword", "email", "verificationCode", "trueCode", "beforeRouteLeave", "to", "from", "next", "computed", "passwordMismatch", "methods", "sendVerificationCode", "console", "log", "$axios", "post", "then", "res", "code", "$message", "success", "error", "message", "catch", "err", "register", "$router", "push", "goToLogin"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\pages\\Register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"register-container\">\r\n    <div class=\"register-card fade-in\">\r\n      <a-button @click=\"goToLogin\" type=\"default\" class=\"back-to-login-btn\">\r\n        <ArrowLeftOutlined :style=\"{ fontSize: '18px' }\"/>\r\n      </a-button>\r\n      <div class=\"register-header\">\r\n        <h1 class=\"register-title\">用户注册</h1>\r\n      </div>\r\n      <div class=\"register-form\">\r\n        <a-input\r\n          v-model:value=\"username\"\r\n          placeholder=\"用户名\"\r\n          size=\"large\"\r\n          class=\"register-input slide-in-up delay-1\">\r\n        </a-input>\r\n\r\n        <a-input-password\r\n          v-model:value=\"password\"\r\n          :class=\"{ 'error-input': passwordMismatch }\"\r\n          placeholder=\"密码\"\r\n          size=\"large\"\r\n          class=\"register-input slide-in-up delay-2\">\r\n        </a-input-password>\r\n\r\n        <a-input-password\r\n          v-model:value=\"confirmPassword\"\r\n          :class=\"{ 'error-input': passwordMismatch }\"\r\n          placeholder=\"确认密码\"\r\n          size=\"large\"\r\n          class=\"register-input slide-in-up delay-3\">\r\n        </a-input-password>\r\n\r\n        <a-input\r\n          v-model:value=\"email\"\r\n          placeholder=\"邮箱\"\r\n          size=\"large\"\r\n          class=\"register-input slide-in-up delay-4\">\r\n        </a-input>\r\n\r\n        <div class=\"verification-code-container slide-in-up delay-5\">\r\n          <a-input\r\n            v-model:value=\"verificationCode\"\r\n            placeholder=\"验证码\"\r\n            size=\"large\"\r\n            class=\"verification-input\">\r\n          </a-input>\r\n          <a-button\r\n            @click=\"sendVerificationCode\"\r\n            type=\"default\"\r\n            size=\"large\"\r\n            class=\"verification-btn\">\r\n            发送验证码\r\n          </a-button>\r\n        </div>\r\n\r\n        <div class=\"register-actions\">\r\n          <a-button\r\n            @click=\"register\"\r\n            type=\"primary\"\r\n            size=\"large\"\r\n            class=\"register-button slide-in-up delay-6\">\r\n            注册\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n  <script>\r\n  import { ArrowLeftOutlined } from '@ant-design/icons-vue';\r\n  export default {\r\n    name: 'RegisterView',\r\n    components: {\r\n        ArrowLeftOutlined\r\n    },\r\n    data() {\r\n      return {\r\n        username: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        email: '',\r\n        verificationCode: '',\r\n        trueCode: ''\r\n      };\r\n    },\r\n    // 刚切换到注册页面时，清空输入框\r\n    beforeRouteLeave(to, from, next) {\r\n      this.username = '';\r\n      this.password = '';\r\n      this.confirmPassword = '';\r\n      this.email = '';\r\n      this.verificationCode = '';\r\n      this.trueCode = '';\r\n      next();\r\n    },\r\n    computed: {\r\n      passwordMismatch() {\r\n        return this.password !== this.confirmPassword;\r\n      }\r\n    },\r\n    methods: {\r\n      sendVerificationCode() {\r\n        // 实现发送验证码的逻辑\r\n        console.log('发送验证码到邮箱:', this.email);\r\n        this.$axios.post('/sendVerificationCode', {\r\n          email: this.email\r\n        }).then((res) => {\r\n          if (res.data.code === 200) {\r\n            this.trueCode=res.data.verificationCode;\r\n            this.$message.success('验证码发送成功');\r\n          } else {\r\n            console.error('验证码发送失败:', res.data.message);\r\n          }\r\n        }).catch((err) => {\r\n          console.error('验证码发送失败:', err);\r\n        });\r\n      },\r\n      register() {\r\n        // 实现注册逻辑\r\n        console.log('用户名:', this.username);\r\n        console.log('密码:', this.password);\r\n        console.log('确认密码:', this.confirmPassword);\r\n        console.log('邮箱:', this.email);\r\n        console.log('邮箱验证码:', this.verificationCode);\r\n\r\n        if (this.passwordMismatch) {\r\n          // 处理密码不匹配的情况，可以显示提示信息或者采取其他操作\r\n          this.$message.error('两次输入的密码不一致');\r\n          // 清空确认密码输入框\r\n          this.confirmPassword = '';\r\n          return;\r\n        }\r\n        if(this.verificationCode!==this.trueCode){\r\n          this.$message.error('验证码错误');\r\n          // 清空验证码输入框\r\n          this.verificationCode = '';\r\n          return;\r\n        }\r\n        // 发送注册请求\r\n        this.$axios.post('/register', {\r\n          username: this.username,\r\n          password: this.password,\r\n          email: this.email\r\n        }).then((res) => {\r\n          if (res.data.code === 200) {\r\n            this.$message.success('注册成功');\r\n            // 注册成功后，可以将用户重定向到登录页面\r\n            this.$router.push('/');\r\n          } else {\r\n            this.$message.error('注册失败:' + res.data.message);\r\n          }\r\n        }).catch((err) => {\r\n          console.error('注册失败:', err);\r\n          this.$message.error('注册失败，未知错误!');\r\n        });\r\n\r\n      },\r\n      goToLogin() {\r\n        // 点击返回登录按钮时的逻辑，重定向到登录页面\r\n        this.$router.push('/');\r\n      }\r\n    }\r\n  };\r\n  </script>\r\n\r\n  <style scoped>\r\n.register-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 100vh;\r\n  background: url('../assets/bg.png') no-repeat center center/cover;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.register-card {\r\n  width: 450px;\r\n  padding: 40px;\r\n  background: rgba(255, 255, 255, 0.85);\r\n  border-radius: 16px;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.5);\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.register-card:hover {\r\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.register-card::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 5px;\r\n  height: 100%;\r\n  background: #1890ff;\r\n  opacity: 0.8;\r\n}\r\n\r\n.register-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.logo-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.register-logo {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: contain;\r\n  filter: drop-shadow(0 2px 8px rgba(24, 144, 255, 0.2));\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.register-logo:hover {\r\n  transform: scale(1.05);\r\n  filter: drop-shadow(0 4px 12px rgba(24, 144, 255, 0.3));\r\n}\r\n\r\n.register-title {\r\n  font-size: 32px;\r\n  color: #1890ff;\r\n  margin-bottom: 20px;\r\n  font-weight: 600;\r\n}\r\n\r\n.register-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.register-input {\r\n  margin-bottom: 20px;\r\n  height: 50px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.verification-code-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.verification-input {\r\n  flex: 1;\r\n  height: 50px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.verification-btn {\r\n  height: 50px;\r\n  border-radius: 8px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.register-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.register-button {\r\n  height: 50px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  transition: all 0.3s;\r\n  background-color: #1890ff;\r\n  border: none;\r\n}\r\n\r\n.register-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 7px 14px rgba(24, 144, 255, 0.2), 0 3px 6px rgba(0, 0, 0, 0.08);\r\n  background-color: #40a9ff;\r\n}\r\n\r\n.back-to-login-btn {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  border: none;\r\n  background: transparent;\r\n  box-shadow: none;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.back-to-login-btn:hover {\r\n  color: #1890ff;\r\n  transform: translateX(-3px);\r\n}\r\n\r\n.error-input {\r\n  border-color: #ff4d4f !important;\r\n}\r\n\r\n/* 动画效果 */\r\n.fade-in {\r\n  animation: fadeIn 0.8s ease-in-out;\r\n}\r\n\r\n.slide-in-up {\r\n  animation: slideInUp 0.5s ease-in-out;\r\n}\r\n\r\n.delay-1 {\r\n  animation-delay: 0.1s;\r\n}\r\n\r\n.delay-2 {\r\n  animation-delay: 0.2s;\r\n}\r\n\r\n.delay-3 {\r\n  animation-delay: 0.3s;\r\n}\r\n\r\n.delay-4 {\r\n  animation-delay: 0.4s;\r\n}\r\n\r\n.delay-5 {\r\n  animation-delay: 0.5s;\r\n}\r\n\r\n.delay-6 {\r\n  animation-delay: 0.6s;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    transform: translateY(20px);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 600px) {\r\n  .register-card {\r\n    width: 90%;\r\n    padding: 30px 20px;\r\n  }\r\n\r\n  .register-title {\r\n    font-size: 28px;\r\n  }\r\n}\r\n  </style>\r\n"], "mappings": ";AAuEE,SAASA,iBAAgB,QAAS,uBAAuB;AACzD,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;IACRF;EACJ,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,EAAE;MACnBC,KAAK,EAAE,EAAE;MACTC,gBAAgB,EAAE,EAAE;MACpBC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EACD;EACAC,gBAAgBA,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE;IAC/B,IAAI,CAACT,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,eAAc,GAAI,EAAE;IACzB,IAAI,CAACC,KAAI,GAAI,EAAE;IACf,IAAI,CAACC,gBAAe,GAAI,EAAE;IAC1B,IAAI,CAACC,QAAO,GAAI,EAAE;IAClBI,IAAI,CAAC,CAAC;EACR,CAAC;EACDC,QAAQ,EAAE;IACRC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACV,QAAO,KAAM,IAAI,CAACC,eAAe;IAC/C;EACF,CAAC;EACDU,OAAO,EAAE;IACPC,oBAAoBA,CAAA,EAAG;MACrB;MACAC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAACZ,KAAK,CAAC;MACpC,IAAI,CAACa,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAE;QACxCd,KAAK,EAAE,IAAI,CAACA;MACd,CAAC,CAAC,CAACe,IAAI,CAAEC,GAAG,IAAK;QACf,IAAIA,GAAG,CAACpB,IAAI,CAACqB,IAAG,KAAM,GAAG,EAAE;UACzB,IAAI,CAACf,QAAQ,GAACc,GAAG,CAACpB,IAAI,CAACK,gBAAgB;UACvC,IAAI,CAACiB,QAAQ,CAACC,OAAO,CAAC,SAAS,CAAC;QAClC,OAAO;UACLR,OAAO,CAACS,KAAK,CAAC,UAAU,EAAEJ,GAAG,CAACpB,IAAI,CAACyB,OAAO,CAAC;QAC7C;MACF,CAAC,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAK;QAChBZ,OAAO,CAACS,KAAK,CAAC,UAAU,EAAEG,GAAG,CAAC;MAChC,CAAC,CAAC;IACJ,CAAC;IACDC,QAAQA,CAAA,EAAG;MACT;MACAb,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACf,QAAQ,CAAC;MAClCc,OAAO,CAACC,GAAG,CAAC,KAAK,EAAE,IAAI,CAACd,QAAQ,CAAC;MACjCa,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACb,eAAe,CAAC;MAC1CY,OAAO,CAACC,GAAG,CAAC,KAAK,EAAE,IAAI,CAACZ,KAAK,CAAC;MAC9BW,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACX,gBAAgB,CAAC;MAE5C,IAAI,IAAI,CAACO,gBAAgB,EAAE;QACzB;QACA,IAAI,CAACU,QAAQ,CAACE,KAAK,CAAC,YAAY,CAAC;QACjC;QACA,IAAI,CAACrB,eAAc,GAAI,EAAE;QACzB;MACF;MACA,IAAG,IAAI,CAACE,gBAAgB,KAAG,IAAI,CAACC,QAAQ,EAAC;QACvC,IAAI,CAACgB,QAAQ,CAACE,KAAK,CAAC,OAAO,CAAC;QAC5B;QACA,IAAI,CAACnB,gBAAe,GAAI,EAAE;QAC1B;MACF;MACA;MACA,IAAI,CAACY,MAAM,CAACC,IAAI,CAAC,WAAW,EAAE;QAC5BjB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBE,KAAK,EAAE,IAAI,CAACA;MACd,CAAC,CAAC,CAACe,IAAI,CAAEC,GAAG,IAAK;QACf,IAAIA,GAAG,CAACpB,IAAI,CAACqB,IAAG,KAAM,GAAG,EAAE;UACzB,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,MAAM,CAAC;UAC7B;UACA,IAAI,CAACM,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;QACxB,OAAO;UACL,IAAI,CAACR,QAAQ,CAACE,KAAK,CAAC,OAAM,GAAIJ,GAAG,CAACpB,IAAI,CAACyB,OAAO,CAAC;QACjD;MACF,CAAC,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAK;QAChBZ,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEG,GAAG,CAAC;QAC3B,IAAI,CAACL,QAAQ,CAACE,KAAK,CAAC,YAAY,CAAC;MACnC,CAAC,CAAC;IAEJ,CAAC;IACDO,SAASA,CAAA,EAAG;MACV;MACA,IAAI,CAACF,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;IACxB;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}