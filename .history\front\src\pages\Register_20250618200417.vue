<template>
  <div class="register-container">
    <div class="register-card fade-in">
      <a-button @click="goToLogin" type="default" class="back-to-login-btn">
        <ArrowLeftOutlined :style="{ fontSize: '18px' }"/>
      </a-button>
      <div class="register-header">
        <h1 class="register-title">用户注册</h1>
      </div>
      <div class="register-form">
        <a-input
          v-model:value="username"
          placeholder="用户名"
          size="large"
          class="register-input slide-in-up delay-1">
        </a-input>

        <a-input-password
          v-model:value="password"
          :class="{ 'error-input': passwordMismatch }"
          placeholder="密码"
          size="large"
          class="register-input slide-in-up delay-2">
        </a-input-password>

        <a-input-password
          v-model:value="confirmPassword"
          :class="{ 'error-input': passwordMismatch }"
          placeholder="确认密码"
          size="large"
          class="register-input slide-in-up delay-3">
        </a-input-password>

        <a-input
          v-model:value="email"
          placeholder="邮箱"
          size="large"
          class="register-input slide-in-up delay-4">
        </a-input>

        <div class="verification-code-container slide-in-up delay-5">
          <a-input
            v-model:value="verificationCode"
            placeholder="验证码"
            size="large"
            class="verification-input">
          </a-input>
          <a-button
            @click="sendVerificationCode"
            type="default"
            size="large"
            class="verification-btn">
            发送验证码
          </a-button>
        </div>

        <div class="register-actions">
          <a-button
            @click="register"
            type="primary"
            size="large"
            class="register-button slide-in-up delay-6">
            注册
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

  <script>
  import { ArrowLeftOutlined } from '@ant-design/icons-vue';
  export default {
    name: 'RegisterView',
    components: {
        ArrowLeftOutlined
    },
    data() {
      return {
        username: '',
        password: '',
        confirmPassword: '',
        email: '',
        verificationCode: '',
        trueCode: ''
      };
    },
    // 刚切换到注册页面时，清空输入框
    beforeRouteLeave(to, from, next) {
      this.username = '';
      this.password = '';
      this.confirmPassword = '';
      this.email = '';
      this.verificationCode = '';
      this.trueCode = '';
      next();
    },
    computed: {
      passwordMismatch() {
        return this.password !== this.confirmPassword;
      }
    },
    methods: {
      sendVerificationCode() {
        // 实现发送验证码的逻辑
        console.log('发送验证码到邮箱:', this.email);
        this.$axios.post('/sendVerificationCode', {
          email: this.email
        }).then((res) => {
          if (res.data.code === 200) {
            this.trueCode=res.data.verificationCode;
            this.$message.success('验证码发送成功');
          } else {
            console.error('验证码发送失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('验证码发送失败:', err);
        });
      },
      register() {
        // 实现注册逻辑
        console.log('用户名:', this.username);
        console.log('密码:', this.password);
        console.log('确认密码:', this.confirmPassword);
        console.log('邮箱:', this.email);
        console.log('邮箱验证码:', this.verificationCode);

        if (this.passwordMismatch) {
          // 处理密码不匹配的情况，可以显示提示信息或者采取其他操作
          this.$message.error('两次输入的密码不一致');
          // 清空确认密码输入框
          this.confirmPassword = '';
          return;
        }
        if(this.verificationCode!==this.trueCode){
          this.$message.error('验证码错误');
          // 清空验证码输入框
          this.verificationCode = '';
          return;
        }
        // 发送注册请求
        this.$axios.post('/register', {
          username: this.username,
          password: this.password,
          email: this.email
        }).then((res) => {
          if (res.data.code === 200) {
            this.$message.success('注册成功');
            // 注册成功后，可以将用户重定向到登录页面
            this.$router.push('/');
          } else {
            this.$message.error('注册失败:' + res.data.message);
          }
        }).catch((err) => {
          console.error('注册失败:', err);
          this.$message.error('注册失败，未知错误!');
        });

      },
      goToLogin() {
        // 点击返回登录按钮时的逻辑，重定向到登录页面
        this.$router.push('/');
      }
    }
  };
  </script>

  <style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: url('../assets/bg.png') no-repeat center center/cover;
  position: relative;
  overflow: hidden;
}

.register-card {
  width: 450px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  position: relative;
  z-index: 1;
  overflow: hidden;
  transition: all 0.3s ease;
}

.register-card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.register-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: #1890ff;
  opacity: 0.8;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-title {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 20px;
  font-weight: 600;
}

.register-form {
  display: flex;
  flex-direction: column;
}

.register-input {
  margin-bottom: 20px;
  height: 50px;
  border-radius: 8px;
}

.verification-code-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.verification-input {
  flex: 1;
  height: 50px;
  border-radius: 8px;
}

.verification-btn {
  height: 50px;
  border-radius: 8px;
  white-space: nowrap;
}

.register-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.register-button {
  height: 50px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s;
  background-color: #1890ff;
  border: none;
}

.register-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(24, 144, 255, 0.2), 0 3px 6px rgba(0, 0, 0, 0.08);
  background-color: #40a9ff;
}

.back-to-login-btn {
  position: absolute;
  top: 15px;
  left: 15px;
  border: none;
  background: transparent;
  box-shadow: none;
  transition: all 0.3s;
}

.back-to-login-btn:hover {
  color: #1890ff;
  transform: translateX(-3px);
}

.error-input {
  border-color: #ff4d4f !important;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.5s ease-in-out;
}

.delay-1 {
  animation-delay: 0.1s;
}

.delay-2 {
  animation-delay: 0.2s;
}

.delay-3 {
  animation-delay: 0.3s;
}

.delay-4 {
  animation-delay: 0.4s;
}

.delay-5 {
  animation-delay: 0.5s;
}

.delay-6 {
  animation-delay: 0.6s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式调整 */
@media (max-width: 600px) {
  .register-card {
    width: 90%;
    padding: 30px 20px;
  }

  .register-title {
    font-size: 28px;
  }
}
  </style>
