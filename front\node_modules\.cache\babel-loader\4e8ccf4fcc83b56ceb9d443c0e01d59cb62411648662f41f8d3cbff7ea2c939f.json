{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, resolveDynamicComponent as _resolveDynamicComponent, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-67a98cef\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"user-view-container\"\n};\nconst _hoisted_2 = {\n  class: \"sidebar fade-in\"\n};\nconst _hoisted_3 = {\n  class: \"user-profile\"\n};\nconst _hoisted_4 = {\n  class: \"avatar-container\"\n};\nconst _hoisted_5 = {\n  class: \"avatar-text\"\n};\nconst _hoisted_6 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"avatar-status\"\n}, null, -1 /* HOISTED */));\nconst _hoisted_7 = {\n  class: \"username\"\n};\nconst _hoisted_8 = {\n  class: \"user-role\"\n};\nconst _hoisted_9 = {\n  class: \"menu-items\"\n};\nconst _hoisted_10 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \"个人信息\", -1 /* HOISTED */));\nconst _hoisted_11 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \"修改密码\", -1 /* HOISTED */));\nconst _hoisted_12 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \"修改邮箱\", -1 /* HOISTED */));\nconst _hoisted_13 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \"用户管理\", -1 /* HOISTED */));\nconst _hoisted_14 = {\n  class: \"bottom-actions\"\n};\nconst _hoisted_15 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \"返回首页\", -1 /* HOISTED */));\nconst _hoisted_16 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \"退出登录\", -1 /* HOISTED */));\nconst _hoisted_17 = {\n  class: \"content-area slide-in-right\"\n};\nconst _hoisted_18 = {\n  class: \"content-header\"\n};\nconst _hoisted_19 = {\n  class: \"page-title\"\n};\nconst _hoisted_20 = {\n  key: 0\n};\nconst _hoisted_21 = {\n  key: 1\n};\nconst _hoisted_22 = {\n  key: 2\n};\nconst _hoisted_23 = {\n  key: 3\n};\nconst _hoisted_24 = {\n  class: \"content-body\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_a_avatar = _resolveComponent(\"a-avatar\");\n  const _component_IdcardOutlined = _resolveComponent(\"IdcardOutlined\");\n  const _component_a_button = _resolveComponent(\"a-button\");\n  const _component_InsuranceOutlined = _resolveComponent(\"InsuranceOutlined\");\n  const _component_MailOutlined = _resolveComponent(\"MailOutlined\");\n  const _component_TeamOutlined = _resolveComponent(\"TeamOutlined\");\n  const _component_HomeFilled = _resolveComponent(\"HomeFilled\");\n  const _component_LoginOutlined = _resolveComponent(\"LoginOutlined\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_a_avatar, {\n    size: 80,\n    class: \"user-avatar\"\n  }, {\n    icon: _withCtx(() => [_createElementVNode(\"span\", _hoisted_5, _toDisplayString($options.getAvatarText()), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }), _hoisted_6]), _createElementVNode(\"h2\", _hoisted_7, _toDisplayString(_ctx.$store.state.username), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_8, _toDisplayString(_ctx.$store.state.is_admin ? '管理员' : '普通用户'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_a_button, {\n    onClick: _cache[0] || (_cache[0] = $event => $options.changeComponent('UserInfo')),\n    class: _normalizeClass(['menu-item', {\n      'selected': $data.currentComponent === 'UserInfo'\n    }]),\n    type: \"text\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_IdcardOutlined), _hoisted_10]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_a_button, {\n    onClick: _cache[1] || (_cache[1] = $event => $options.changeComponent('UserChangePassword')),\n    class: _normalizeClass(['menu-item', {\n      'selected': $data.currentComponent === 'UserChangePassword'\n    }]),\n    type: \"text\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_InsuranceOutlined), _hoisted_11]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_a_button, {\n    onClick: _cache[2] || (_cache[2] = $event => $options.changeComponent('UserChangeEmail')),\n    class: _normalizeClass(['menu-item', {\n      'selected': $data.currentComponent === 'UserChangeEmail'\n    }]),\n    type: \"text\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_MailOutlined), _hoisted_12]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _ctx.$store.state.is_admin ? (_openBlock(), _createBlock(_component_a_button, {\n    key: 0,\n    onClick: _cache[3] || (_cache[3] = $event => $options.changeComponent('UserAdmin')),\n    class: _normalizeClass(['menu-item', {\n      'selected': $data.currentComponent === 'UserAdmin'\n    }]),\n    type: \"text\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_TeamOutlined), _hoisted_13]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_a_button, {\n    onClick: $options.goToMain,\n    type: \"primary\",\n    class: \"action-button home-button\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_HomeFilled), _hoisted_15]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_a_button, {\n    onClick: $options.goToLogin,\n    type: \"danger\",\n    class: \"action-button logout-button\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_LoginOutlined), _hoisted_16]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"h1\", _hoisted_19, [$data.currentComponent === 'UserInfo' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_20, \"个人信息\")) : $data.currentComponent === 'UserChangePassword' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_21, \"修改密码\")) : $data.currentComponent === 'UserChangeEmail' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_22, \"修改邮箱\")) : $data.currentComponent === 'UserAdmin' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_23, \"用户管理\")) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_24, [(_openBlock(), _createBlock(_resolveDynamicComponent($data.currentComponent)))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "key", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_a_avatar", "size", "icon", "_withCtx", "_hoisted_5", "_toDisplayString", "$options", "getAvatarText", "_", "_hoisted_6", "_hoisted_7", "_ctx", "$store", "state", "username", "_hoisted_8", "is_admin", "_hoisted_9", "_component_a_button", "onClick", "_cache", "$event", "changeComponent", "_normalizeClass", "$data", "currentComponent", "type", "default", "_component_IdcardOutlined", "_hoisted_10", "_component_InsuranceOutlined", "_hoisted_11", "_component_MailOutlined", "_hoisted_12", "_createBlock", "_component_TeamOutlined", "_hoisted_13", "_createCommentVNode", "_hoisted_14", "goToMain", "_component_HomeFilled", "_hoisted_15", "goToLogin", "_component_LoginOutlined", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_resolveDynamicComponent"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\pages\\User.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-view-container\">\r\n    <div class=\"sidebar fade-in\">\r\n      <div class=\"user-profile\">\r\n        <div class=\"avatar-container\">\r\n          <a-avatar :size=\"80\" class=\"user-avatar\">\r\n            <template #icon>\r\n              <span class=\"avatar-text\">{{ getAvatarText() }}</span>\r\n            </template>\r\n          </a-avatar>\r\n          <div class=\"avatar-status\"></div>\r\n        </div>\r\n        <h2 class=\"username\">{{ $store.state.username }}</h2>\r\n        <p class=\"user-role\">{{ $store.state.is_admin ? '管理员' : '普通用户' }}</p>\r\n      </div>\r\n\r\n      <div class=\"menu-items\">\r\n        <a-button\r\n          @click=\"changeComponent('UserInfo')\"\r\n          :class=\"['menu-item', { 'selected': currentComponent === 'UserInfo' }]\"\r\n          type=\"text\"\r\n        >\r\n          <IdcardOutlined />\r\n          <span>个人信息</span>\r\n        </a-button>\r\n\r\n        <a-button\r\n          @click=\"changeComponent('UserChangePassword')\"\r\n          :class=\"['menu-item', { 'selected': currentComponent === 'UserChangePassword' }]\"\r\n          type=\"text\"\r\n        >\r\n          <InsuranceOutlined />\r\n          <span>修改密码</span>\r\n        </a-button>\r\n\r\n        <a-button\r\n          @click=\"changeComponent('UserChangeEmail')\"\r\n          :class=\"['menu-item', { 'selected': currentComponent === 'UserChangeEmail' }]\"\r\n          type=\"text\"\r\n        >\r\n          <MailOutlined />\r\n          <span>修改邮箱</span>\r\n        </a-button>\r\n\r\n        <a-button\r\n          v-if=\"$store.state.is_admin\"\r\n          @click=\"changeComponent('UserAdmin')\"\r\n          :class=\"['menu-item', { 'selected': currentComponent === 'UserAdmin' }]\"\r\n          type=\"text\"\r\n        >\r\n          <TeamOutlined />\r\n          <span>用户管理</span>\r\n        </a-button>\r\n      </div>\r\n\r\n      <div class=\"bottom-actions\">\r\n        <a-button @click=\"goToMain\" type=\"primary\" class=\"action-button home-button\">\r\n          <HomeFilled />\r\n          <span>返回首页</span>\r\n        </a-button>\r\n\r\n        <a-button @click=\"goToLogin\" type=\"danger\" class=\"action-button logout-button\">\r\n          <LoginOutlined />\r\n          <span>退出登录</span>\r\n        </a-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"content-area slide-in-right\">\r\n      <div class=\"content-header\">\r\n        <h1 class=\"page-title\">\r\n          <span v-if=\"currentComponent === 'UserInfo'\">个人信息</span>\r\n          <span v-else-if=\"currentComponent === 'UserChangePassword'\">修改密码</span>\r\n          <span v-else-if=\"currentComponent === 'UserChangeEmail'\">修改邮箱</span>\r\n          <span v-else-if=\"currentComponent === 'UserAdmin'\">用户管理</span>\r\n        </h1>\r\n      </div>\r\n\r\n      <div class=\"content-body\">\r\n        <component :is=\"currentComponent\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserInfo from '../components/user_info.vue';\r\nimport UserChangePassword from '../components/user_ch_pwd.vue';\r\nimport UserChangeEmail from '../components/user_ch_mail.vue';\r\nimport UserAdmin from '../components/user_admin.vue';\r\nimport { LoginOutlined, HomeFilled, TeamOutlined, MailOutlined, IdcardOutlined, InsuranceOutlined} from '@ant-design/icons-vue';\r\n\r\nexport default {\r\n  name: 'UserView',\r\n  data() {\r\n    return {\r\n      currentComponent: 'UserInfo',\r\n    };\r\n  },\r\n  methods: {\r\n    changeComponent(componentName) {\r\n      this.currentComponent = componentName;\r\n    },\r\n    goToLogin() {\r\n      this.currentComponent = 'UserInfo';\r\n      this.$router.push('/');\r\n    },\r\n    goToMain() {\r\n      this.currentComponent = 'UserInfo';\r\n      this.$router.push('/main');\r\n    },\r\n    getAvatarText() {\r\n      // 获取用户名的首字母或前两个字符作为头像文本\r\n      const username = this.$store.state.username;\r\n      if (!username) return '?';\r\n\r\n      // 如果用户名是中文，取第一个字符\r\n      if (/[\\u4e00-\\u9fa5]/.test(username)) {\r\n        return username.substring(0, 1);\r\n      }\r\n\r\n      // 如果是英文，取首字母大写\r\n      return username.substring(0, 1).toUpperCase();\r\n    },\r\n  },\r\n  components: {\r\n    UserInfo,\r\n    UserChangePassword,\r\n    UserChangeEmail,\r\n    IdcardOutlined,\r\n    TeamOutlined,\r\n    UserAdmin,\r\n    LoginOutlined,\r\n    HomeFilled,\r\n    MailOutlined,\r\n    InsuranceOutlined\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.user-view-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 247, 255, 0.85) 100%);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  margin: 20px;\r\n  position: relative;\r\n}\r\n\r\n.user-view-container::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-image: url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%231890ff' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E\");\r\n  opacity: 0.5;\r\n  z-index: 0;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 侧边栏样式 */\r\n.sidebar {\r\n  width: 280px;\r\n  background-color: #1890ff;\r\n  color: white;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 30px 0;\r\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.sidebar::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: url('../assets/decorative_pattern.svg') no-repeat center center;\r\n  background-size: 200%;\r\n  opacity: 0.05;\r\n  z-index: 0;\r\n}\r\n\r\n.user-profile {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.user-logo-container {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.user-logo {\r\n  width: 60px;\r\n  height: 60px;\r\n  object-fit: contain;\r\n  filter: drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3));\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-logo:hover {\r\n  transform: scale(1.1);\r\n  filter: drop-shadow(0 4px 12px rgba(255, 255, 255, 0.4));\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n  margin-bottom: 15px;\r\n  display: inline-block;\r\n}\r\n\r\n.user-avatar {\r\n  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);\r\n  border: 3px solid rgba(255, 255, 255, 0.3);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-avatar:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);\r\n  border: 3px solid rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.avatar-text {\r\n  font-size: 36px;\r\n  font-weight: bold;\r\n  color: white;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.avatar-status {\r\n  position: absolute;\r\n  bottom: 5px;\r\n  right: 5px;\r\n  width: 15px;\r\n  height: 15px;\r\n  background-color: #52c41a;\r\n  border-radius: 50%;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);\r\n  }\r\n}\r\n\r\n.username {\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n  margin: 10px 0 5px;\r\n  color: white;\r\n}\r\n\r\n.user-role {\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  margin: 0;\r\n}\r\n\r\n.menu-items {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 10px 15px;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  margin-bottom: 10px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  background: transparent;\r\n  border: none;\r\n  text-align: left;\r\n  font-size: 16px;\r\n}\r\n\r\n.menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  transform: translateX(5px);\r\n}\r\n\r\n.menu-item.selected {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  font-weight: 500;\r\n}\r\n\r\n.menu-item .anticon {\r\n  margin-right: 12px;\r\n  font-size: 18px;\r\n}\r\n\r\n.bottom-actions {\r\n  padding: 20px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.action-button {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 10px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n  font-size: 16px;\r\n}\r\n\r\n.action-button .anticon {\r\n  margin-right: 8px;\r\n}\r\n\r\n.home-button {\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.home-button:hover {\r\n  background-color: #40a9ff;\r\n  border-color: #40a9ff;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.logout-button {\r\n  background-color: #f56565;\r\n  border-color: #f56565;\r\n}\r\n\r\n.logout-button:hover {\r\n  background-color: #e53e3e;\r\n  border-color: #e53e3e;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 30px;\r\n  overflow-y: auto;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(245, 250, 255, 0.9) 100%);\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.content-area::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-image: url(\"data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%234299e1' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E\");\r\n  z-index: -1;\r\n  opacity: 0.7;\r\n  pointer-events: none;\r\n}\r\n\r\n.content-header {\r\n  margin-bottom: 30px;\r\n  border-bottom: 1px solid rgba(226, 232, 240, 0.6);\r\n  padding-bottom: 15px;\r\n  position: relative;\r\n}\r\n\r\n.content-header::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: -1px;\r\n  left: 0;\r\n  width: 100px;\r\n  height: 3px;\r\n  background-color: #1890ff;\r\n  border-radius: 3px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #1890ff;\r\n  margin: 0;\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.content-body {\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  border-radius: 12px;\r\n  padding: 25px;\r\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);\r\n  min-height: 500px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(226, 232, 240, 0.8);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.content-body::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 5px;\r\n  background-color: #1890ff;\r\n  opacity: 0.7;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .user-view-container {\r\n    flex-direction: column;\r\n    margin: 0;\r\n    height: auto;\r\n    min-height: 100vh;\r\n  }\r\n\r\n  .sidebar {\r\n    width: 100%;\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .content-area {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAkB;;EAGjBA,KAAK,EAAC;AAAa;gEAG7BC,mBAAA,CAAiC;EAA5BD,KAAK,EAAC;AAAe;;EAExBA,KAAK,EAAC;AAAU;;EACjBA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;iEAOnBC,mBAAA,CAAiB,cAAX,MAAI;iEASVA,mBAAA,CAAiB,cAAX,MAAI;iEASVA,mBAAA,CAAiB,cAAX,MAAI;iEAUVA,mBAAA,CAAiB,cAAX,MAAI;;EAITD,KAAK,EAAC;AAAgB;iEAGvBC,mBAAA,CAAiB,cAAX,MAAI;iEAKVA,mBAAA,CAAiB,cAAX,MAAI;;EAKXD,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAY;;EAtE9BE,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EA8EWF,KAAK,EAAC;AAAc;;;;;;;;;;uBA7E7BG,mBAAA,CAiFM,OAjFNC,UAiFM,GAhFJH,mBAAA,CAgEM,OAhENI,UAgEM,GA/DJJ,mBAAA,CAWM,OAXNK,UAWM,GAVJL,mBAAA,CAOM,OAPNM,UAOM,GANJC,YAAA,CAIWC,mBAAA;IAJAC,IAAI,EAAE,EAAE;IAAEV,KAAK,EAAC;;IACdW,IAAI,EAAAC,QAAA,CACb,MAAsD,CAAtDX,mBAAA,CAAsD,QAAtDY,UAAsD,EAAAC,gBAAA,CAAzBC,QAAA,CAAAC,aAAa,mB;IAPxDC,CAAA;MAUUC,UAAiC,C,GAEnCjB,mBAAA,CAAqD,MAArDkB,UAAqD,EAAAL,gBAAA,CAA7BM,IAAA,CAAAC,MAAM,CAACC,KAAK,CAACC,QAAQ,kBAC7CtB,mBAAA,CAAqE,KAArEuB,UAAqE,EAAAV,gBAAA,CAA7CM,IAAA,CAAAC,MAAM,CAACC,KAAK,CAACG,QAAQ,kC,GAG/CxB,mBAAA,CAqCM,OArCNyB,UAqCM,GApCJlB,YAAA,CAOWmB,mBAAA;IANRC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEf,QAAA,CAAAgB,eAAe;IACtB/B,KAAK,EAnBhBgC,eAAA;MAAA,YAmB8CC,KAAA,CAAAC,gBAAgB;IAAA;IACpDC,IAAI,EAAC;;IApBfC,OAAA,EAAAxB,QAAA,CAsBU,MAAkB,CAAlBJ,YAAA,CAAkB6B,yBAAA,GAClBC,WAAiB,C;IAvB3BrB,CAAA;gCA0BQT,YAAA,CAOWmB,mBAAA;IANRC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEf,QAAA,CAAAgB,eAAe;IACtB/B,KAAK,EA5BhBgC,eAAA;MAAA,YA4B8CC,KAAA,CAAAC,gBAAgB;IAAA;IACpDC,IAAI,EAAC;;IA7BfC,OAAA,EAAAxB,QAAA,CA+BU,MAAqB,CAArBJ,YAAA,CAAqB+B,4BAAA,GACrBC,WAAiB,C;IAhC3BvB,CAAA;gCAmCQT,YAAA,CAOWmB,mBAAA;IANRC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEf,QAAA,CAAAgB,eAAe;IACtB/B,KAAK,EArChBgC,eAAA;MAAA,YAqC8CC,KAAA,CAAAC,gBAAgB;IAAA;IACpDC,IAAI,EAAC;;IAtCfC,OAAA,EAAAxB,QAAA,CAwCU,MAAgB,CAAhBJ,YAAA,CAAgBiC,uBAAA,GAChBC,WAAiB,C;IAzC3BzB,CAAA;gCA6CgBG,IAAA,CAAAC,MAAM,CAACC,KAAK,CAACG,QAAQ,I,cAD7BkB,YAAA,CAQWhB,mBAAA;IApDnBzB,GAAA;IA8CW0B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEf,QAAA,CAAAgB,eAAe;IACtB/B,KAAK,EA/ChBgC,eAAA;MAAA,YA+C8CC,KAAA,CAAAC,gBAAgB;IAAA;IACpDC,IAAI,EAAC;;IAhDfC,OAAA,EAAAxB,QAAA,CAkDU,MAAgB,CAAhBJ,YAAA,CAAgBoC,uBAAA,GAChBC,WAAiB,C;IAnD3B5B,CAAA;kCAAA6B,mBAAA,e,GAuDM7C,mBAAA,CAUM,OAVN8C,WAUM,GATJvC,YAAA,CAGWmB,mBAAA;IAHAC,OAAK,EAAEb,QAAA,CAAAiC,QAAQ;IAAEb,IAAI,EAAC,SAAS;IAACnC,KAAK,EAAC;;IAxDzDoC,OAAA,EAAAxB,QAAA,CAyDU,MAAc,CAAdJ,YAAA,CAAcyC,qBAAA,GACdC,WAAiB,C;IA1D3BjC,CAAA;kCA6DQT,YAAA,CAGWmB,mBAAA;IAHAC,OAAK,EAAEb,QAAA,CAAAoC,SAAS;IAAEhB,IAAI,EAAC,QAAQ;IAACnC,KAAK,EAAC;;IA7DzDoC,OAAA,EAAAxB,QAAA,CA8DU,MAAiB,CAAjBJ,YAAA,CAAiB4C,wBAAA,GACjBC,WAAiB,C;IA/D3BpC,CAAA;sCAoEIhB,mBAAA,CAaM,OAbNqD,WAaM,GAZJrD,mBAAA,CAOM,OAPNsD,WAOM,GANJtD,mBAAA,CAKK,MALLuD,WAKK,GAJSvB,KAAA,CAAAC,gBAAgB,mB,cAA5B/B,mBAAA,CAAwD,QAvElEsD,WAAA,EAuEuD,MAAI,KAChCxB,KAAA,CAAAC,gBAAgB,6B,cAAjC/B,mBAAA,CAAuE,QAxEjFuD,WAAA,EAwEsE,MAAI,KAC/CzB,KAAA,CAAAC,gBAAgB,0B,cAAjC/B,mBAAA,CAAoE,QAzE9EwD,WAAA,EAyEmE,MAAI,KAC5C1B,KAAA,CAAAC,gBAAgB,oB,cAAjC/B,mBAAA,CAA8D,QA1ExEyD,WAAA,EA0E6D,MAAI,KA1EjEd,mBAAA,e,KA8EM7C,mBAAA,CAEM,OAFN4D,WAEM,I,cADJlB,YAAA,CAAoCmB,wBA/E5C,CA+EwB7B,KAAA,CAAAC,gBAAgB,I"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}