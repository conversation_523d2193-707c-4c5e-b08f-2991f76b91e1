{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-425331d6\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-card fade-in\"\n};\nconst _hoisted_3 = /*#__PURE__*/_createStaticVNode(\"<div class=\\\"login-header\\\" data-v-425331d6><h1 class=\\\"login-title\\\" data-v-425331d6>城市智能治理平台</h1><p class=\\\"login-subtitle\\\" data-v-425331d6>多模态检测技术助力城市精细化管理</p><div class=\\\"theme-badges\\\" data-v-425331d6><span class=\\\"theme-badge\\\" data-v-425331d6>视频分析</span><span class=\\\"theme-badge\\\" data-v-425331d6>智能监测</span><span class=\\\"theme-badge\\\" data-v-425331d6>城市治理</span></div></div>\", 1);\nconst _hoisted_4 = {\n  class: \"login-form\"\n};\nconst _hoisted_5 = {\n  class: \"login-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_user_outlined = _resolveComponent(\"user-outlined\");\n  const _component_a_input = _resolveComponent(\"a-input\");\n  const _component_lock_outlined = _resolveComponent(\"lock-outlined\");\n  const _component_a_input_password = _resolveComponent(\"a-input-password\");\n  const _component_a_button = _resolveComponent(\"a-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_hoisted_3, _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_a_input, {\n    value: $data.username,\n    \"onUpdate:value\": _cache[0] || (_cache[0] = $event => $data.username = $event),\n    placeholder: \"用户名\",\n    size: \"large\",\n    class: \"login-input slide-in-up delay-1\"\n  }, {\n    prefix: _withCtx(() => [_createVNode(_component_user_outlined)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"value\"]), _createVNode(_component_a_input_password, {\n    value: $data.password,\n    \"onUpdate:value\": _cache[1] || (_cache[1] = $event => $data.password = $event),\n    placeholder: \"密码\",\n    size: \"large\",\n    class: \"login-input slide-in-up delay-2\"\n  }, {\n    prefix: _withCtx(() => [_createVNode(_component_lock_outlined)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"value\"]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_a_button, {\n    onClick: $options.login,\n    type: \"primary\",\n    size: \"large\",\n    class: \"login-button slide-in-up delay-3\",\n    loading: $data.loading\n  }, {\n    default: _withCtx(() => [_createTextVNode(\" 登录 \")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"loading\"]), _createVNode(_component_a_button, {\n    onClick: $options.register,\n    type: \"default\",\n    size: \"large\",\n    class: \"register-button slide-in-up delay-4\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(\" 注册 \")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])])])]);\n}", "map": {"version": 3, "names": ["class", "_createStaticVNode", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_a_input", "value", "$data", "username", "_cache", "$event", "placeholder", "size", "prefix", "_withCtx", "_component_user_outlined", "_", "_component_a_input_password", "password", "_component_lock_outlined", "_hoisted_5", "_component_a_button", "onClick", "$options", "login", "type", "loading", "default", "_createTextVNode", "register"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\pages\\LogIn.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"login-card fade-in\">\r\n      <div class=\"login-header\">\r\n        <h1 class=\"login-title\">城市智能治理平台</h1>\r\n        <p class=\"login-subtitle\">多模态检测技术助力城市精细化管理</p>\r\n        <div class=\"theme-badges\">\r\n          <span class=\"theme-badge\">视频分析</span>\r\n          <span class=\"theme-badge\">智能监测</span>\r\n          <span class=\"theme-badge\">城市治理</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"login-form\">\r\n        <a-input\r\n          v-model:value=\"username\"\r\n          placeholder=\"用户名\"\r\n          size=\"large\"\r\n          class=\"login-input slide-in-up delay-1\"\r\n        >\r\n          <template #prefix><user-outlined /></template>\r\n        </a-input>\r\n\r\n        <a-input-password\r\n          v-model:value=\"password\"\r\n          placeholder=\"密码\"\r\n          size=\"large\"\r\n          class=\"login-input slide-in-up delay-2\"\r\n        >\r\n          <template #prefix><lock-outlined /></template>\r\n        </a-input-password>\r\n\r\n        <div class=\"login-actions\">\r\n          <a-button\r\n            @click=\"login\"\r\n            type=\"primary\"\r\n            size=\"large\"\r\n            class=\"login-button slide-in-up delay-3\"\r\n            :loading=\"loading\"\r\n          >\r\n            登录\r\n          </a-button>\r\n          <a-button\r\n            @click=\"register\"\r\n            type=\"default\"\r\n            size=\"large\"\r\n            class=\"register-button slide-in-up delay-4\"\r\n          >\r\n            注册\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { UserOutlined, LockOutlined } from '@ant-design/icons-vue';\r\n\r\nexport default {\r\n  name: 'LogInView',\r\n  components: {\r\n    UserOutlined,\r\n    LockOutlined\r\n  },\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      loading: false\r\n    };\r\n  },\r\n  beforeRouteLeave(to, from, next) {\r\n    this.username = '';\r\n    this.password = '';\r\n    next();\r\n  },\r\n  methods: {\r\n    login() {\r\n      if (!this.username || !this.password) {\r\n        this.$message.warning('请输入用户名和密码');\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n\r\n      // 发送登录请求\r\n      this.$axios.post('/login', {\r\n        username: this.username,\r\n        password: this.password\r\n      }).then((res) => {\r\n        if(res.data.code === 200) {\r\n          this.$message.success('登录成功，欢迎回来！');\r\n\r\n          // 登录成功后，将用户信息保存到vuex中\r\n          this.$store.dispatch('updateUsername', res.data.user.username);\r\n          this.$store.dispatch('updatePassword', res.data.user.password);\r\n          this.$store.dispatch('updateEmail', res.data.user.email);\r\n          this.$store.dispatch('updateIsAdmin', res.data.user.is_admin);\r\n\r\n          // 跳转到首页\r\n          this.$router.push('/main');\r\n        } else {\r\n          // 登录失败，提示错误信息\r\n          this.$message.error(res.data.message || '登录失败，请检查用户名和密码');\r\n        }\r\n      }).catch((err) => {\r\n        console.error('登录失败:', err);\r\n        this.$message.error('登录请求失败，请稍后再试');\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    register() {\r\n      // 跳转到注册页面\r\n      this.$router.push('/register');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.login-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 100vh;\r\n  background: url('../assets/bg.png') no-repeat center center/cover;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.login-card {\r\n  width: 400px;\r\n  padding: 40px;\r\n  background: rgba(255, 255, 255, 0.85);\r\n  border-radius: 16px;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.5);\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.login-card:hover {\r\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.login-card::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 5px;\r\n  height: 100%;\r\n  background: #1890ff;\r\n  opacity: 0.8;\r\n}\r\n\r\n.login-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.logo-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.login-logo {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: contain;\r\n  filter: drop-shadow(0 2px 8px rgba(24, 144, 255, 0.2));\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.login-logo:hover {\r\n  transform: scale(1.05);\r\n  filter: drop-shadow(0 4px 12px rgba(24, 144, 255, 0.3));\r\n}\r\n\r\n.login-title {\r\n  font-size: 32px;\r\n  color: #1890ff;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.login-subtitle {\r\n  color: #666;\r\n  font-size: 16px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.theme-badges {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.theme-badge {\r\n  display: inline-block;\r\n  padding: 4px 12px;\r\n  background: rgba(24, 144, 255, 0.1);\r\n  color: #1890ff;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  border: 1px solid rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n.login-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.login-input {\r\n  margin-bottom: 20px;\r\n  height: 50px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.login-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.login-button, .register-button {\r\n  height: 50px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.login-button {\r\n  background-color: #1890ff;\r\n  border: none;\r\n}\r\n\r\n.login-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 7px 14px rgba(24, 144, 255, 0.2), 0 3px 6px rgba(0, 0, 0, 0.08);\r\n  background-color: #40a9ff;\r\n}\r\n\r\n.register-button:hover {\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 600px) {\r\n  .login-card {\r\n    width: 90%;\r\n    padding: 30px 20px;\r\n  }\r\n\r\n  .login-title {\r\n    font-size: 28px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAoB;gCAFnCC,kBAAA;;EAYWD,KAAK,EAAC;AAAY;;EAmBhBA,KAAK,EAAC;AAAe;;;;;;;uBA9BhCE,mBAAA,CAmDM,OAnDNC,UAmDM,GAlDJC,mBAAA,CAiDM,OAjDNC,UAiDM,GAhDJC,UAQM,EACNF,mBAAA,CAsCM,OAtCNG,UAsCM,GArCJC,YAAA,CAOUC,kBAAA;IANAC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAdjC,kBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAcyBH,KAAA,CAAAC,QAAQ,GAAAE,MAAA;IACvBC,WAAW,EAAC,KAAK;IACjBC,IAAI,EAAC,OAAO;IACZhB,KAAK,EAAC;;IAEKiB,MAAM,EAAAC,QAAA,CAAC,MAAiB,CAAjBV,YAAA,CAAiBW,wBAAA,E;IAnB7CC,CAAA;gCAsBQZ,YAAA,CAOmBa,2BAAA;IANTX,KAAK,EAAEC,KAAA,CAAAW,QAAQ;IAvBjC,kBAAAT,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuByBH,KAAA,CAAAW,QAAQ,GAAAR,MAAA;IACvBC,WAAW,EAAC,IAAI;IAChBC,IAAI,EAAC,OAAO;IACZhB,KAAK,EAAC;;IAEKiB,MAAM,EAAAC,QAAA,CAAC,MAAiB,CAAjBV,YAAA,CAAiBe,wBAAA,E;IA5B7CH,CAAA;gCA+BQhB,mBAAA,CAkBM,OAlBNoB,UAkBM,GAjBJhB,YAAA,CAQWiB,mBAAA;IAPRC,OAAK,EAAEC,QAAA,CAAAC,KAAK;IACbC,IAAI,EAAC,SAAS;IACdb,IAAI,EAAC,OAAO;IACZhB,KAAK,EAAC,kCAAkC;IACvC8B,OAAO,EAAEnB,KAAA,CAAAmB;;IArCtBC,OAAA,EAAAb,QAAA,CAsCW,MAED,CAxCVc,gBAAA,CAsCW,MAED,E;IAxCVZ,CAAA;6CAyCUZ,YAAA,CAOWiB,mBAAA;IANRC,OAAK,EAAEC,QAAA,CAAAM,QAAQ;IAChBJ,IAAI,EAAC,SAAS;IACdb,IAAI,EAAC,OAAO;IACZhB,KAAK,EAAC;;IA7ClB+B,OAAA,EAAAb,QAAA,CA8CW,MAED,CAhDVc,gBAAA,CA8CW,MAED,E;IAhDVZ,CAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}