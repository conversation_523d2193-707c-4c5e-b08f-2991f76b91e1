{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { UserOutlined, LockOutlined } from '@ant-design/icons-vue';\nexport default {\n  name: 'LogInView',\n  components: {\n    UserOutlined,\n    LockOutlined\n  },\n  data() {\n    return {\n      username: '',\n      password: '',\n      loading: false\n    };\n  },\n  beforeRouteLeave(to, from, next) {\n    this.username = '';\n    this.password = '';\n    next();\n  },\n  methods: {\n    login() {\n      if (!this.username || !this.password) {\n        this.$message.warning('请输入用户名和密码');\n        return;\n      }\n      this.loading = true;\n\n      // 发送登录请求\n      this.$axios.post('/login', {\n        username: this.username,\n        password: this.password\n      }).then(res => {\n        if (res.data.code === 200) {\n          this.$message.success('登录成功，欢迎回来！');\n\n          // 登录成功后，将用户信息保存到vuex中\n          this.$store.dispatch('updateUsername', res.data.user.username);\n          this.$store.dispatch('updatePassword', res.data.user.password);\n          this.$store.dispatch('updateEmail', res.data.user.email);\n          this.$store.dispatch('updateIsAdmin', res.data.user.is_admin);\n\n          // 跳转到首页\n          this.$router.push('/main');\n        } else {\n          // 登录失败，提示错误信息\n          this.$message.error(res.data.message || '登录失败，请检查用户名和密码');\n        }\n      }).catch(err => {\n        console.error('登录失败:', err);\n        this.$message.error('登录请求失败，请稍后再试');\n      }).finally(() => {\n        this.loading = false;\n      });\n    },\n    register() {\n      // 跳转到注册页面\n      this.$router.push('/register');\n    }\n  }\n};", "map": {"version": 3, "names": ["UserOutlined", "LockOutlined", "name", "components", "data", "username", "password", "loading", "beforeRouteLeave", "to", "from", "next", "methods", "login", "$message", "warning", "$axios", "post", "then", "res", "code", "success", "$store", "dispatch", "user", "email", "is_admin", "$router", "push", "error", "message", "catch", "err", "console", "finally", "register"], "sources": ["C:\\Users\\<USER>\\Desktop\\TLG_ALL\\front\\src\\pages\\LogIn.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"login-card fade-in\">\r\n      <div class=\"login-header\">\r\n        <div class=\"logo-container\">\r\n          <img src=\"../assets/logo.svg\" alt=\"Logo\" class=\"login-logo\" />\r\n        </div>\r\n        <h1 class=\"login-title\">城市智能治理平台</h1>\r\n        <p class=\"login-subtitle\">多模态检测技术助力城市精细化管理</p>\r\n        <div class=\"theme-badges\">\r\n          <span class=\"theme-badge\">视频分析</span>\r\n          <span class=\"theme-badge\">智能监测</span>\r\n          <span class=\"theme-badge\">城市治理</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"login-form\">\r\n        <a-input\r\n          v-model:value=\"username\"\r\n          placeholder=\"用户名\"\r\n          size=\"large\"\r\n          class=\"login-input slide-in-up delay-1\"\r\n        >\r\n          <template #prefix><user-outlined /></template>\r\n        </a-input>\r\n\r\n        <a-input-password\r\n          v-model:value=\"password\"\r\n          placeholder=\"密码\"\r\n          size=\"large\"\r\n          class=\"login-input slide-in-up delay-2\"\r\n        >\r\n          <template #prefix><lock-outlined /></template>\r\n        </a-input-password>\r\n\r\n        <div class=\"login-actions\">\r\n          <a-button\r\n            @click=\"login\"\r\n            type=\"primary\"\r\n            size=\"large\"\r\n            class=\"login-button slide-in-up delay-3\"\r\n            :loading=\"loading\"\r\n          >\r\n            登录\r\n          </a-button>\r\n          <a-button\r\n            @click=\"register\"\r\n            type=\"default\"\r\n            size=\"large\"\r\n            class=\"register-button slide-in-up delay-4\"\r\n          >\r\n            注册\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { UserOutlined, LockOutlined } from '@ant-design/icons-vue';\r\n\r\nexport default {\r\n  name: 'LogInView',\r\n  components: {\r\n    UserOutlined,\r\n    LockOutlined\r\n  },\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      loading: false\r\n    };\r\n  },\r\n  beforeRouteLeave(to, from, next) {\r\n    this.username = '';\r\n    this.password = '';\r\n    next();\r\n  },\r\n  methods: {\r\n    login() {\r\n      if (!this.username || !this.password) {\r\n        this.$message.warning('请输入用户名和密码');\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n\r\n      // 发送登录请求\r\n      this.$axios.post('/login', {\r\n        username: this.username,\r\n        password: this.password\r\n      }).then((res) => {\r\n        if(res.data.code === 200) {\r\n          this.$message.success('登录成功，欢迎回来！');\r\n\r\n          // 登录成功后，将用户信息保存到vuex中\r\n          this.$store.dispatch('updateUsername', res.data.user.username);\r\n          this.$store.dispatch('updatePassword', res.data.user.password);\r\n          this.$store.dispatch('updateEmail', res.data.user.email);\r\n          this.$store.dispatch('updateIsAdmin', res.data.user.is_admin);\r\n\r\n          // 跳转到首页\r\n          this.$router.push('/main');\r\n        } else {\r\n          // 登录失败，提示错误信息\r\n          this.$message.error(res.data.message || '登录失败，请检查用户名和密码');\r\n        }\r\n      }).catch((err) => {\r\n        console.error('登录失败:', err);\r\n        this.$message.error('登录请求失败，请稍后再试');\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    register() {\r\n      // 跳转到注册页面\r\n      this.$router.push('/register');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.login-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 100vh;\r\n  background: url('../assets/bg.png') no-repeat center center/cover;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.login-card {\r\n  width: 400px;\r\n  padding: 40px;\r\n  background: rgba(255, 255, 255, 0.85);\r\n  border-radius: 16px;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.5);\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.login-card:hover {\r\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.login-card::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 5px;\r\n  height: 100%;\r\n  background: #1890ff;\r\n  opacity: 0.8;\r\n}\r\n\r\n.login-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.login-title {\r\n  font-size: 32px;\r\n  color: #1890ff;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.login-subtitle {\r\n  color: #666;\r\n  font-size: 16px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.theme-badges {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.theme-badge {\r\n  display: inline-block;\r\n  padding: 4px 12px;\r\n  background: rgba(24, 144, 255, 0.1);\r\n  color: #1890ff;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  border: 1px solid rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n.login-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.login-input {\r\n  margin-bottom: 20px;\r\n  height: 50px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.login-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.login-button, .register-button {\r\n  height: 50px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.login-button {\r\n  background-color: #1890ff;\r\n  border: none;\r\n}\r\n\r\n.login-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 7px 14px rgba(24, 144, 255, 0.2), 0 3px 6px rgba(0, 0, 0, 0.08);\r\n  background-color: #40a9ff;\r\n}\r\n\r\n.register-button:hover {\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 600px) {\r\n  .login-card {\r\n    width: 90%;\r\n    padding: 30px 20px;\r\n  }\r\n\r\n  .login-title {\r\n    font-size: 28px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AA2DA,SAASA,YAAY,EAAEC,YAAW,QAAS,uBAAuB;AAElE,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACVH,YAAY;IACZC;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EACDC,gBAAgBA,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE;IAC/B,IAAI,CAACN,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClBK,IAAI,CAAC,CAAC;EACR,CAAC;EACDC,OAAO,EAAE;IACPC,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC,IAAI,CAACR,QAAO,IAAK,CAAC,IAAI,CAACC,QAAQ,EAAE;QACpC,IAAI,CAACQ,QAAQ,CAACC,OAAO,CAAC,WAAW,CAAC;QAClC;MACF;MAEA,IAAI,CAACR,OAAM,GAAI,IAAI;;MAEnB;MACA,IAAI,CAACS,MAAM,CAACC,IAAI,CAAC,QAAQ,EAAE;QACzBZ,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC,CAACY,IAAI,CAAEC,GAAG,IAAK;QACf,IAAGA,GAAG,CAACf,IAAI,CAACgB,IAAG,KAAM,GAAG,EAAE;UACxB,IAAI,CAACN,QAAQ,CAACO,OAAO,CAAC,YAAY,CAAC;;UAEnC;UACA,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,gBAAgB,EAAEJ,GAAG,CAACf,IAAI,CAACoB,IAAI,CAACnB,QAAQ,CAAC;UAC9D,IAAI,CAACiB,MAAM,CAACC,QAAQ,CAAC,gBAAgB,EAAEJ,GAAG,CAACf,IAAI,CAACoB,IAAI,CAAClB,QAAQ,CAAC;UAC9D,IAAI,CAACgB,MAAM,CAACC,QAAQ,CAAC,aAAa,EAAEJ,GAAG,CAACf,IAAI,CAACoB,IAAI,CAACC,KAAK,CAAC;UACxD,IAAI,CAACH,MAAM,CAACC,QAAQ,CAAC,eAAe,EAAEJ,GAAG,CAACf,IAAI,CAACoB,IAAI,CAACE,QAAQ,CAAC;;UAE7D;UACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;QAC5B,OAAO;UACL;UACA,IAAI,CAACd,QAAQ,CAACe,KAAK,CAACV,GAAG,CAACf,IAAI,CAAC0B,OAAM,IAAK,gBAAgB,CAAC;QAC3D;MACF,CAAC,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAK;QAChBC,OAAO,CAACJ,KAAK,CAAC,OAAO,EAAEG,GAAG,CAAC;QAC3B,IAAI,CAAClB,QAAQ,CAACe,KAAK,CAAC,cAAc,CAAC;MACrC,CAAC,CAAC,CAACK,OAAO,CAAC,MAAM;QACf,IAAI,CAAC3B,OAAM,GAAI,KAAK;MACtB,CAAC,CAAC;IACJ,CAAC;IACD4B,QAAQA,CAAA,EAAG;MACT;MACA,IAAI,CAACR,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;IAChC;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}